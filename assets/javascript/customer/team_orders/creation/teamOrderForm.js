import {
  PICKER_DATE_FORMAT,
  isPackageOrder,
  isOrderEdit,
  isRecurringOrder,
  isRecurringOrderExtension,
  toggleMultipleDaysLabel,
  setupMultiDaySelector,
  refreshSupplierList,
  checkSupplierAvailability,
} from './common.js';

require('jquery-ui/ui/widgets/datepicker');
require('jquery-ui-timepicker-addon/dist/jquery-ui-timepicker-addon');

const MULTI_DAY_LIMIT = 15;

const STEPS = {
  'step-1': 'order-details',
  'step-2': 'attendees-selection',
  'step-3': 'supplier-selection',
  'step-4': 'payment-details',
};

export default function teamOrderForm(el, props) {
  const $el = $(el);
  const $form = $el.find('.team-order-form');
  bindEvents($el, $form);
  setupDeliveryDatePicker($form);
  setupPOSelect($form);
  setupLevelSelect($form);
  autoProceedForOrderExtension($form);
}

function bindEvents($wrapper, $form) {
  $wrapper.on('click', '.team-order-step-btn', function (e) {
    changePanel(e, $wrapper, $form);
  });
  $form.on('click', '.team-order-type-selection button', function (e) {
    changeTeamOrderType(e, $form);
  });
  $form.on('click', '.team-order-levels-handle', function (e) {
    toggleAttendeeLevels(e, $form);
  });
  $wrapper.on('click', '.place-order-btn', function (e) {
    submitTeamOrder(e, $wrapper, $form);
  });
  $wrapper.on('click', '.extend-order-btn', function (e) {
    extendTeamOrder(e, $wrapper, $form);
  });
  $(document).on('click', '#package-update-modal .place-order-btn', function (e) {
    submitTeamOrder(e, $wrapper, $form);
  });

  $wrapper.on('click', '.supplier-day', function (e) {
    chooseDeliveryDay(e, $wrapper, $form);
  });
}

function chooseDeliveryDay(event, $wrapper, $form) {
  const $deliveryDay = $(event.currentTarget);
  const $deliveryDays = $wrapper.find('.supplier-day');
  $deliveryDays.removeClass('active');
  $deliveryDay.addClass('active');

  checkSupplierAvailability($form);

  $wrapper.find('.team-supplier .choose-supplier-btn').removeClass('selected-supplier');
  const delivery_date = $deliveryDay.data('date');

  // mark already selected supplier for date as selected
  const deliverySuppliers = JSON.parse($form.find('input[name="order[delivery_suppliers]"]').val());
  const hasSupplierForDay =
    deliverySuppliers[delivery_date] && Object.keys(deliverySuppliers[delivery_date]).length !== 0;
  if (hasSupplierForDay) {
    const supplier_id = Object.keys(deliverySuppliers[delivery_date])[0];
    const $selectedSupplier = $(`.team-supplier[data-supplier-id=${supplier_id}]`);
    if ($selectedSupplier.length) {
      $selectedSupplier.find('.choose-supplier-btn').addClass('selected-supplier');
    }
  }
}

function setupLevelSelect($form) {
  const $levelSelect = $form.find('select[name="order[team_order_detail_attributes][levels][names][]"]');
  $levelSelect.select2({
    tags: true,
    tokenSeparators: [','],
    dropdownParent: $form,
    multiple: true,
  });
}

function setupPOSelect($form) {
  $form.find('select#order_cpo_id').select2({
    tags: true,
    tokenSeparators: [',', ' '],
    dropdownParent: $form,
  });
}

function autoProceedForOrderExtension($form) {
  if (isRecurringOrderExtension($form)) {
    $form.find('.order-details .team-order-step-btn').click(); // go directly to supplier selection
  }
}

function changeDatepickerInfo($form) {
  if (isPackageOrder($form) || isRecurringOrder($form)) {
    showMultipleDatesTooltip(true);
    changeDatepickerPlaceholder('multiple');
  } else {
    showMultipleDatesTooltip(false);
    changeDatepickerPlaceholder();
    $('.multiple-days-label').addClass('hidden');
  }
}

function changeDatepickerPlaceholder(type) {
  const $datepicker = $('.datepicker');
  if (type === 'multiple') {
    return $datepicker.attr('placeholder', 'Select Multiple Dates');
  }
  $datepicker.attr('placeholder', 'Select a Date and Time');
}

function showMultipleDatesTooltip(show) {
  const $tooltip = $('.whats-this__multiple');
  if (show) {
    return $tooltip.removeClass('hidden');
  }
  $tooltip.addClass('hidden');
}

function setupDeliveryDatePicker($form) {
  const min_date_time = new Date(new Date().setMinutes(15 * Math.ceil(new Date().getMinutes() / 15)));
  const $teamOrderDates = $form.find('input[name="order[delivery_dates]"]');

  // https://stackoverflow.com/questions/1452066/jquery-ui-datepicker-multiple-date-selections
  function addOrRemoveDate(date, dateTimePicker) {
    const formattedDate = $.datepicker.formatDate(PICKER_DATE_FORMAT, new Date(date));
    const dateWithTime = `${formattedDate} ${date.split(' ')[1]}`;

    const teamOrderDates = JSON.parse($teamOrderDates.val());
    const formattedDates = teamOrderDates.map(function (date, idx) {
      return $.datepicker.formatDate(PICKER_DATE_FORMAT, new Date(date));
    });
    const index = jQuery.inArray(formattedDate, formattedDates);

    if (isPackageOrder($form)) {
      if (index >= 0 && date == teamOrderDates[index]) {
        teamOrderDates.splice(index, 1);
        if (teamOrderDates.length > 0) {
          var newValue = teamOrderDates[teamOrderDates.length - 1];
        } else {
          var newValue = '';
        }
        dateTimePicker = $(dateTimePicker)[0];
        setTimeout(function () {
          $(dateTimePicker.input).val(newValue);
        }, 200); // delay to override value set by datepicker
      } else if (index >= 0) {
        // time update
        teamOrderDates.splice(index, 1);
        teamOrderDates.push(dateWithTime);
      } else if (teamOrderDates.length < MULTI_DAY_LIMIT) {
        teamOrderDates.push(dateWithTime);
      }
      toggleMultipleDaysLabel($form, teamOrderDates);
    } else if (index == -1) {
      teamOrderDates.splice(index, 1);
      teamOrderDates.push(dateWithTime);
    }
    $teamOrderDates.val(JSON.stringify(teamOrderDates));
  }

  $('input.datepicker').datetimepicker({
    monthNames: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    controlType: 'select',
    dateFormat: PICKER_DATE_FORMAT,
    timeFormat: 'HH:mm',
    stepMinute: 15,
    minDateTime: min_date_time,
    onSelect(dateText, inst) {
      addOrRemoveDate(dateText, inst);
    },
    beforeShowDay(date) {
      const teamOrderDates = JSON.parse($teamOrderDates.val());
      const formattedDates = teamOrderDates.map(function (date, idx) {
        return $.datepicker.formatDate(PICKER_DATE_FORMAT, new Date(date));
      });
      const formattedDate = $.datepicker.formatDate(PICKER_DATE_FORMAT, date);
      const gotDate = $.inArray(formattedDate, formattedDates);
      if (isOrderEdit($form)) {
        return [false, 'ui-state-multi-unselect'];
      }
      if (isRecurringOrder($form)) {
        const $selectedDays = $form.find('.open-team-days input[type=checkbox]:checked');
        const selected_days = $selectedDays
          .map(function (idx, el) {
            return parseInt($(el).val());
          })
          .get();
        return [selected_days.indexOf(date.getDay()) != -1, ''];
      }
      if (gotDate >= 0) {
        return [true, 'ui-state-multi-select'];
      }
      return [true, 'ui-state-multi-unselect'];
    },
  });
}

function changeTeamOrderType(event, $form) {
  event.preventDefault(); // prevent validation;
  const $button = $(event.currentTarget);
  if ($button.hasClass('active')) return;

  $form.find('.team-order-type-selection button').removeClass('active');
  $button.addClass('active');
  changeDatepickerInfo($form);

  const $teamOrderDates = $form.find('input[name="order[delivery_dates]"]');
  const $teamOrderSuppliers = $form.find('input[name="order[delivery_suppliers]"]');
  $teamOrderDates.val('[]');
  $teamOrderSuppliers.val('{}');

  $('input.datepicker').val('');
}

function changePanel(event, $wrapper, $form) {
  const $button = $(event.currentTarget);
  const step = $button.data('step');
  const currentStep = $button.data('current-step');
  const panel = STEPS[step];
  const currentPanel = STEPS[currentStep];
  const validPanel = validatePanel(currentPanel, $form);
  if (validPanel) {
    toggleActivePanel(panel, $wrapper, $form);
  }
}

function validatePanel(currentPanel, $form) {
  let validForm = true;
  switch (currentPanel) {
    case 'order-details':
      validForm = isValidForm($form);
      if (validForm) {
        validForm = hasDeliveryAddress($form);
      }
      break;
    case 'attendees-selection':
      validForm = hasAttendees($form);
      break;
    case 'supplier-selection':
      validForm = hasSuppliers($form);
      break;
    default:
      break;
  }
  return validForm;
}

function hasAttendees($form) {
  const attendee_ids = $form
    .find('input[name="order[attendee_ids][]"]')
    .map(function (idx, el) {
      const $attendee = $(el);
      return $attendee.val() ? $attendee.val() : null;
    })
    .get();
  if (attendee_ids.length <= 0) {
    $form.yordarPOP({
      title: 'Attendee Selection',
      innerContent: 'You need to select at least 1 attendee for the event',
      cancel: false,
    });
  }
  return attendee_ids.length > 0;
}

function hasSuppliers($form) {
  let hasSelectedSuppliers = false;
  const $teamOrderDates = $form.find('input[name="order[delivery_dates]"]');
  const $teamOrderSuppliers = $form.find('input[name="order[delivery_suppliers]"]');
  const deliverySuppliers = JSON.parse($teamOrderSuppliers.val());
  const deliveryDates = JSON.parse($teamOrderDates.val());
  const supplier_ids = new Array();

  deliveryDates.map(function (date) {
    const formattedDate = $.datepicker.formatDate(PICKER_DATE_FORMAT, new Date(date));
    if (deliverySuppliers[formattedDate] && Object.keys(deliverySuppliers[formattedDate]).length !== 0) {
      supplier_ids.push(Object.keys(deliverySuppliers[formattedDate])[0]);
    }
  });
  hasSelectedSuppliers = supplier_ids.length == deliveryDates.length;

  if (!hasSelectedSuppliers) {
    if (isPackageOrder($form) || isRecurringOrder($form)) {
      var message = 'You need to select a Supplier for each day of the event';
    } else {
      var message = 'You need to select a Supplier for the event';
    }
    $form.yordarPOP({
      title: 'Supplier Selection',
      innerContent: message,
      cancel: false,
    });
  }
  return hasSelectedSuppliers;
}

function toggleActivePanel(panel, $wrapper, $form) {
  $wrapper.find('.team-order-panel').addClass('hidden');
  $wrapper.find(`.${panel}`).removeClass('hidden');
  window.scrollTo(0, 0);

  // load suppliers if not already loaded
  if (['attendees-selection', 'supplier-selection'].indexOf != -1 && $form.find('.team-supplier').length == 0) {
    const suburbId = $form.find('#suburb_id').val();
    localStorage.setItem('suburb_id', suburbId);
    refreshSupplierList($form);
  }

  if (panel == 'supplier-selection') {
    if (isRecurringOrder($form)) {
      setupRecurringDeliveryDates($form);
    } else {
      setupMultiDaySelector($form);
    }

    if (!isPackageOrder($form) || isRecurringOrder($form)) {
      checkSupplierAvailability($form);
    }

    updateSupplierBestForTeamCount($form);
  }
}

// setup delivery dates based on the order details form's day and date inputs
function setupRecurringDeliveryDates($form) {
  const $selectedDays = $form.find('.open-team-days input[type=checkbox]:checked');
  var selected_days = $selectedDays
    .map(function (idx, el) {
      return parseInt($(el).val());
    })
    .get();

  const delivery_date = $form.find('input.datepicker').val();
  const current_date = new Date(delivery_date);

  let number_of_days = isRecurringOrderExtension($form) ? 7 : 14;
  if (current_date.getDay() > selected_days[0]) {
  // if the selected date's week-day is greater the the first selected week day
    number_of_days += 6 - current_date.getDay();
  }
  const dayms = number_of_days * 24 * 60 * 60 * 1000;
  const end_date = new Date(current_date.getTime() + dayms);

  var selected_days = $selectedDays
    .map(function (idx, el) {
      return parseInt($(el).val());
    })
    .get();
  const teamOrderDates = [];
  while (current_date < end_date) {
    if (selected_days.indexOf(current_date.getDay()) != -1) {
      const formattedDate = $.datepicker.formatDate(PICKER_DATE_FORMAT, current_date);
      const dateWithTime = `${formattedDate} ${delivery_date.split(' ')[1]}`;
      teamOrderDates.push(dateWithTime);
    }
    current_date.setDate(current_date.getDate() + 1);
  }
  const $teamOrderDates = $form.find('input[name="order[delivery_dates]"]');
  $teamOrderDates.val(JSON.stringify(teamOrderDates));
  setupMultiDaySelector($form);

  if (!isRecurringOrderExtension($form) && !$teamOrderDates.data('recurring_supplier_popup')) {
    $form.yordarPOP({
      container: '#yordarPopUp',
      title: 'Recurring Team Order Suppliers',
      innerContent: 'For initial setup we require you to select suppliers for 2 complete weeks.',
      submit: 'Ok',
      cancel: false,
    });
    $teamOrderDates.data('recurring_supplier_popup', true);
  }
}

function updateSupplierBestForTeamCount($form) {
  const $suppliers = $form.find('.team-supplier');
  const budget = $form.find('input[name="order[team_order_detail_attributes][budget]"]').val();
  if (budget) {
    const $budgetHeader = $form.find('.best-for-header');
    $budgetHeader.attr('title', `<span class='text-center'>Based on budget of:<br /> $${budget} (per head)</span>`);

    $suppliers.each(function (idx, el) {
      const $supplier = $(el);
      let bestFor = parseInt($supplier.data('minimum-spend') / budget);
      if (bestFor < 1) bestFor = 1;
      $supplier.find('.best-for-teams').text(bestFor);
    });
  }
}

// checks the hidden field for delivery address
function hasDeliveryAddress($form) {
  const deliveryAddress = $form.find('input[name="order[delivery_address]"]').val();
  if (!deliveryAddress) {
    $form.yordarPOP({
      title: 'Team Order Delivery',
      innerContent: 'We need a valid street address to find the right suppliers',
      cancel: false,
    });
  }
  return !!deliveryAddress;
}

function isValidForm($form) {
  sanitizeDeliveryDates($form);
  $form.foundation('validateForm');
  // subsection is for partially validating a single form
  let order_details_errors;
  order_details_errors = $form.find('[data-invalid]').length > 0;
  if (order_details_errors) {
    scrollToValidationErrors($form);
  } else {
    return true;
  }
}

function sanitizeDeliveryDates($form) {
  if (!isPackageOrder($form) && !isRecurringOrder($form)) {
    return false;
  }
  const $teamOrderDates = $form.find('input[name="order[delivery_dates]"]');
  const teamOrderDates = JSON.parse($teamOrderDates.val());
  if (teamOrderDates.length == 0) {
    $form.find('input[name="order[delivery_at]"]').val('').trigger('change');
  }
}

function scrollToValidationErrors($form) {
  const invalidFields = $form.find('[data-invalid]');
  const scrollTo = $(`#${invalidFields[0].id}`).offset().top - 40;
  $('html, body').animate({ scrollTop: scrollTo }, 400);
}

function toggleAttendeeLevels(event, $form) {
  const $link = $(event.currentTarget);
  const $levelsForm = $form.find('.team-order-levels');
  $link.toggleClass('hidden');
  $levelsForm.toggleClass('hidden');
}

function submitTeamOrder(event, $wrapper, $form) {
  const $button = $(event.currentTarget);

  // set delivery suburb
  const suburb_id = $form.find('#suburb_id').val();
  $form.find('input[name="order[delivery_suburb_id]"]').val(suburb_id);

  const $newCardForm = $wrapper.find('form.new-credit-card');
  const order_card = $form.find('input[name="order[credit_card_id]"]').val();

  if ($newCardForm.is(':visible') && !$newCardForm.data('submitting')) {
    $newCardForm.submit(); // handled in customerOrderPaymentForm.js // returns back to here on success
  } else if (order_card) {
    submitOrderWithMode($button, $form);
  } else {
    $(this).yordarPOP({
      container: '#yordarPopUp',
      title: 'Team Order Error!',
      innerContent: 'Please select a Payment method',
      cancel: false,
    });
  }
}

// extend order button on supplier selection panel
function extendTeamOrder(event, $wrapper, $form) {
  event.preventDefault();

  const $button = $(event.currentTarget);
  const hasOrderCard = $form.find('input[name="order[credit_card_id]"]').val();
  const isValidExtension = hasSuppliers($form) && hasOrderCard;
  if (isValidExtension) {
    submitOrderWithMode($button, $form);
  }
}

function submitOrderWithMode($button, $form) {
  const mode = $button.data('mode');
  if (mode) {
    const $modeInput = $form.find('input[name="order[mode]"]');
    $modeInput.val(mode);
    $form.append($modeInput);
  }
  $form.foundation('validateForm');
  if ($form.find('[data-invalid]').length === 0) {
    $button.html(spinner_html);
    $form.submit();
  } else {
    $form.yordarPOP({
      container: '#yordarPopUp',
      title: 'Team Order Error!',
      innerContent: 'Please check form for invalid fields',
      cancel: false,
    });
  }
}
