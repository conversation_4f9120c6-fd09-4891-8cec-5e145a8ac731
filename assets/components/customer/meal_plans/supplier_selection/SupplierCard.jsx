import PropTypes from 'prop-types';
import { formatPrice, calculateBestTeamSize, getSupplierImageUrl } from './supplierSelectionAPI';

const SupplierCard = ({ 
  supplier, 
  isSelected, 
  onSelect, 
  budget,
  isAvailable 
}) => {
  const getRatingClass = (rating) => {
    return rating >= 3 ? 'team-supplier-rating--approval' : '';
  };

  return (
    <li 
      className={`team-supplier team-order-list__row row ${!isAvailable ? 'unavailable' : ''}`}
      data-supplier-id={supplier.id}
      data-minimum-spend={supplier.minimum_spend || 0}
    >
      {/* Supplier name and image */}
      <div className="small-12 columns no-gutter medium-4">
        <div className="small-12 columns no-gutter medium-2 team-supplier-image-container">
          <img
            src={getSupplierImageUrl(supplier.image_id)}
            alt={supplier.company_name || supplier.name}
            style={{ width: '100%', height: 'auto', borderRadius: '4px' }}
          />
        </div>
        <div className="team-order-list__cell team-supplier-name small-12 columns no-gutter medium-10">
          <span className="mobile-tag">Supplier Name: </span>
          {(supplier.company_name || supplier.name)?.substring(0, 40)}
          {supplier.company_name?.length > 40 && '...'}
          
          {supplier.is_new && <span className="team-supplier-new">new</span>}
          {supplier.has_custom_pricing && <span className="team-supplier-custom">custom pricing</span>}
          {supplier.has_custom_menu && <span className="team-supplier-custom">custom menu</span>}
        </div>
      </div>

      {/* Supplier details */}
      <div className="small-12 columns no-gutter medium-6">
        {/* Minimum order */}
        <div className="team-order-list__cell small-12 columns no-gutter medium-3">
          <span className="mobile-tag">Min Order:</span>
          {formatPrice(supplier.minimum_spend)}
        </div>

        {/* Rating */}
        <div className="team-order-list__cell small-12 medium-2 columns no-gutter">
          <span className="mobile-tag">Rating: </span>
          <span className={`team-supplier-rating ${getRatingClass(supplier.rating)}`}>
            {supplier.rating > 0 ? supplier.rating : '-'}
          </span>
        </div>

        {/* Lead time */}
        <div className="team-order-list__cell small-12 columns no-gutter medium-4">
          <span className="mobile-tag">Lead Time: </span>
          {supplier.lead_time || '-'}
        </div>

        {/* Best for team size */}
        <div className="team-order-list__cell small-12 columns no-gutter medium-3">
          <span className="mobile-tag">Best For: </span>
          {calculateBestTeamSize(supplier.minimum_spend, budget)}
        </div>
      </div>

      {/* Action buttons */}
      <div className="small-12 columns no-gutter medium-2">
        <div className="team-order-list__cell team-order-list__actions">
          {/* Menu button */}
          <div>
            <button 
              className="button tiny gray-btn supplier-menu"
              style={{ margin: 0 }}
              onClick={(e) => {
                e.stopPropagation();
                // This would open the menu modal
                // For now, we'll handle this in the parent component
              }}
            >
              Menu
            </button>
          </div>

          {/* Select/Unselect button */}
          <div>
            <button
              className={`small-6 columns choose-supplier-btn team-order-list__btn circle-icon float-right ${
                isSelected ? 'selected-supplier' : ''
              }`}
              type="button"
              onClick={onSelect}
              disabled={!isAvailable}
              title={!isAvailable ? 'Supplier is not available for selected date/time' : ''}
            >
              {isSelected ? '✓' : '+'}
            </button>
          </div>
        </div>
      </div>

      {/* Availability notice */}
      {!isAvailable && (
        <div className="supplier-unavailable-notice">
          <small>* Greyed out suppliers are not available or need more notice</small>
        </div>
      )}
    </li>
  );
};

SupplierCard.propTypes = {
  supplier: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    name: PropTypes.string,
    company_name: PropTypes.string,
    rating: PropTypes.number,
    minimum_spend: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    lead_time: PropTypes.string,
    image_id: PropTypes.string,
    is_new: PropTypes.bool,
    has_custom_pricing: PropTypes.bool,
    has_custom_menu: PropTypes.bool
  }).isRequired,
  isSelected: PropTypes.bool,
  onSelect: PropTypes.func.isRequired,
  budget: PropTypes.number,
  isAvailable: PropTypes.bool
};

SupplierCard.defaultProps = {
  isSelected: false,
  budget: 10,
  isAvailable: true
};

export default SupplierCard;
