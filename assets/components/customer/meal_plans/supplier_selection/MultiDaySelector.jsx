import PropTypes from 'prop-types';
import moment from 'moment';

const MultiDaySelector = ({ 
  days, 
  activeDay, 
  onDaySelect, 
  selectedSuppliers 
}) => {
  const formatDayDisplay = (day) => {
    // Convert day abbreviation to a more readable format with date
    const dayNames = {
      'Mon': 'Monday',
      'Tue': 'Tuesday', 
      'Wed': 'Wednesday',
      'Thu': 'Thursday',
      'Fri': 'Friday'
    };
    
    // Calculate the next occurrence of this day
    const today = moment();
    const dayIndex = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'].indexOf(day);
    const currentDayIndex = today.day() === 0 ? 6 : today.day() - 1; // Convert Sunday=0 to Monday=0
    const daysToAdd = (dayIndex - currentDayIndex + 7) % 7;
    const nextOccurrence = today.clone().add(daysToAdd, 'days');
    
    return {
      shortName: day,
      fullName: dayNames[day],
      date: nextOccurrence.format('D MMM')
    };
  };

  const hasSupplierForDay = (day) => {
    const daySuppliers = selectedSuppliers[day];
    return daySuppliers && Object.keys(daySuppliers).length > 0;
  };

  const getSupplierCountForDay = (day) => {
    const daySuppliers = selectedSuppliers[day];
    return daySuppliers ? Object.keys(daySuppliers).length : 0;
  };

  if (!days || days.length === 0) {
    return null;
  }

  return (
    <div className="multi-day-supplier-selector-container">
      <ul className="multi-day-supplier-selector">
        {days.map((day) => {
          const dayInfo = formatDayDisplay(day);
          const isActive = activeDay === day;
          const hasSupplier = hasSupplierForDay(day);
          const supplierCount = getSupplierCountForDay(day);
          
          return (
            <li
              key={day}
              className={`supplier-day ${isActive ? 'active' : ''} ${hasSupplier ? 'selected-supplier' : ''}`}
              data-date={day}
              onClick={() => onDaySelect(day)}
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  onDaySelect(day);
                }
              }}
            >
              <div className="supplier-day-content">
                <div className="supplier-day-header">
                  <span className="supplier-day-name">{dayInfo.shortName}</span>
                  <span className="supplier-day-date">{dayInfo.date}</span>
                </div>
                
                {hasSupplier && (
                  <div className="supplier-day-status">
                    <span className="supplier-count">
                      {supplierCount} supplier{supplierCount !== 1 ? 's' : ''} selected
                    </span>
                  </div>
                )}
                
                {!hasSupplier && (
                  <div className="supplier-day-status no-supplier">
                    <span className="no-supplier-text">No supplier selected</span>
                  </div>
                )}
              </div>
            </li>
          );
        })}
      </ul>
      
      <div className="multi-day-selector-info">
        <p className="recurring-order-info">
          <small>
            Select different suppliers for each delivery day. You can choose the same supplier 
            for multiple days or mix and match based on your team's preferences.
          </small>
        </p>
      </div>
    </div>
  );
};

MultiDaySelector.propTypes = {
  days: PropTypes.arrayOf(PropTypes.string).isRequired,
  activeDay: PropTypes.string.isRequired,
  onDaySelect: PropTypes.func.isRequired,
  selectedSuppliers: PropTypes.object
};

MultiDaySelector.defaultProps = {
  selectedSuppliers: {}
};

export default MultiDaySelector;
