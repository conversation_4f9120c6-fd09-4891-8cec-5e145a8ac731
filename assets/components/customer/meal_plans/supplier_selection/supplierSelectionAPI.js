import axios from 'axios';
import moment from 'moment';
import { csrfHeaders } from 'utilities/csrfHeaders';

// API endpoints - these should match your Rails routes
const API_ENDPOINTS = {
  refreshSuppliers: '/api/team_orders/refresh_suppliers_list',
  checkAvailability: '/api/team_orders/fetch_suppliers_availability',
  getSupplierMenu: '/api/team_orders/retrieve_supplier_menu',
  checkCutoffHours: '/api/team_orders/fetch_supplier_cutoff_hours_remaining',
};

/**
 * Fetch suppliers list with filtering options
 */
export const fetchSuppliers = async (params) => {
  try {
    const response = await axios.get(API_ENDPOINTS.refreshSuppliers, {
      params: {
        ...params,
        wants_html: false, // We want JSON data, not HTML
      },
      headers: csrfHeaders(),
    });

    return {
      success: true,
      data: response.data,
    };
  } catch (error) {
    console.error('Error fetching suppliers:', error);
    console.error('Error details:', error.response?.data);
    return {
      success: false,
      error: error.response?.data?.message || 'Failed to fetch suppliers',
    };
  }
};

/**
 * Check supplier availability for given dates
 */
export const checkSupplierAvailability = async (supplierIds, deliveryDate, suburbId) => {
  try {
    const response = await axios.get(API_ENDPOINTS.checkAvailability, {
      params: {
        supplier_ids: supplierIds,
        delivery_date: deliveryDate,
        suburb_id: suburbId,
      },
      headers: csrfHeaders(),
    });
    return {
      success: true,
      data: response.data,
    };
  } catch (error) {
    console.error('Error checking supplier availability:', error);
    return {
      success: false,
      error: error.response?.data?.message || 'Failed to check supplier availability',
    };
  }
};

/**
 * Get supplier menu sections
 */
export const getSupplierMenu = async (supplierSlug, deliveryDate, suburbId, selectedMenuSections = []) => {
  try {
    const response = await axios.get(`${API_ENDPOINTS.getSupplierMenu}/${supplierSlug}`, {
      params: {
        delivery_date: deliveryDate,
        suburb_id: suburbId,
        selected_menu_sections: selectedMenuSections,
        wants_html: false,
      },
      headers: csrfHeaders(),
    });
    return {
      success: true,
      data: response.data,
    };
  } catch (error) {
    console.error('Error fetching supplier menu:', error);
    return {
      success: false,
      error: error.response?.data?.message || 'Failed to fetch supplier menu',
    };
  }
};

/**
 * Check supplier cutoff hours remaining
 */
export const checkSupplierCutoffHours = async (supplierId, deliveryDate, suburbId) => {
  try {
    const response = await axios.get(API_ENDPOINTS.checkCutoffHours, {
      params: {
        supplier_id: supplierId,
        delivery_date: deliveryDate,
        suburb_id: suburbId,
      },
      headers: csrfHeaders(),
    });
    return {
      success: true,
      data: response.data,
    };
  } catch (error) {
    console.error('Error checking cutoff hours:', error);
    return {
      success: false,
      error: error.response?.data?.message || 'Failed to check cutoff hours',
    };
  }
};

/**
 * Format supplier data for display
 */
export const formatSupplierData = (supplier) => ({
  id: supplier.id,
  name: supplier.company_name || supplier.name,
  rating: supplier.rating || 0,
  minimumSpend: supplier.minimum_spend,
  leadTime: supplier.lead_time,
  imageId: supplier.image_id,
  isNew: supplier.is_new,
  slug: supplier.slug,
  description: supplier.description,
});

/**
 * Format price for display
 */
export const formatPrice = (price) => {
  if (!price || price === 0) return '-';
  return `$${parseFloat(price).toFixed(2)}`;
};

/**
 * Calculate best team size based on minimum spend and budget
 */
export const calculateBestTeamSize = (minimumSpend, budget) => {
  if (!minimumSpend || !budget) return '-';
  const teamSize = Math.ceil(minimumSpend / budget);
  return `${teamSize}+`;
};

/**
 * Get supplier image URL
 */
export const getSupplierImageUrl = (imageId, options = {}) => {
  if (!imageId) return '/images/default-supplier.png';

  const { width = 600, height = 600, crop = 'fill', quality = 'auto,fl_lossy,f_auto' } = options;

  // This assumes you're using Cloudinary - adjust based on your image service
  return `https://res.cloudinary.com/your-cloud-name/image/upload/w_${width},h_${height},c_${crop},q_${quality}/${imageId}`;
};

/**
 * Validate supplier selection data
 */
export const validateSupplierSelection = (selectedSuppliers, recurringDays) => {
  const errors = [];

  if (!selectedSuppliers || Object.keys(selectedSuppliers).length === 0) {
    errors.push('At least one supplier must be selected');
    return { isValid: false, errors };
  }

  // For recurring orders, check that all selected days have suppliers
  if (recurringDays && recurringDays.length > 0) {
    const missingDays = recurringDays.filter((day) => {
      const daySuppliers = selectedSuppliers[day];
      return !daySuppliers || Object.keys(daySuppliers).length === 0;
    });

    if (missingDays.length > 0) {
      errors.push(`Please select suppliers for: ${missingDays.join(', ')}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Convert selected suppliers to the format expected by the backend
 */
export const formatSelectedSuppliersForSubmission = (selectedSuppliers, mealPlan) => {
  const formatted = {};

  if (mealPlan.is_recurring) {
    // For recurring orders, format by day
    Object.entries(selectedSuppliers).forEach(([day, daySuppliers]) => {
      if (daySuppliers && Object.keys(daySuppliers).length > 0) {
        // Convert day to date format that backend expects
        const deliveryDate = getDeliveryDateForDay(day, mealPlan.delivery_time);
        formatted[deliveryDate] = daySuppliers;
      }
    });
  } else {
    // For single orders, use the delivery date
    const deliveryDate = mealPlan.delivery_time;
    formatted[deliveryDate] = selectedSuppliers.single || {};
  }

  return formatted;
};

/**
 * Get delivery date for a specific day of the week
 */
export const getDeliveryDateForDay = (day, baseDeliveryTime) => {
  const WEEKDAYS = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'];

  const baseDate = moment(baseDeliveryTime);
  const dayIndex = WEEKDAYS.indexOf(day);
  const currentDayIndex = baseDate.day() === 0 ? 6 : baseDate.day() - 1; // Convert Sunday=0 to Monday=0
  const daysToAdd = (dayIndex - currentDayIndex + 7) % 7;

  return baseDate.clone().add(daysToAdd, 'days').format('YYYY-MM-DD HH:mm');
};
