import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

const SupplierFilters = ({ 
  searchKeywords, 
  onSearchChange, 
  filters, 
  onFilterChange 
}) => {
  const [searchInput, setSearchInput] = useState(searchKeywords);
  const [activeDropdown, setActiveDropdown] = useState(null);

  // Filter options - these would typically come from the backend
  const filterOptions = {
    category: {
      header: 'Catering Services',
      sections: {
        'Catering Services': [
          { name: 'category', value: 'breakfast', label: 'Breakfast' },
          { name: 'category', value: 'lunch', label: 'Lunch' },
          { name: 'category', value: 'dinner', label: 'Dinner' },
          { name: 'category', value: 'snacks', label: 'Snacks & Light Meals' },
          { name: 'category', value: 'beverages', label: 'Beverages' },
          { name: 'category', value: 'desserts', label: 'Desserts' }
        ]
      }
    },
    other: {
      header: 'Other Options',
      sections: {
        'Dietary Options': [
          { name: 'other', value: 'vegetarian', label: 'Vegetarian' },
          { name: 'other', value: 'vegan', label: 'Vegan' },
          { name: 'other', value: 'gluten_free', label: 'Gluten Free' },
          { name: 'other', value: 'dairy_free', label: 'Dairy Free' },
          { name: 'other', value: 'halal', label: 'Halal' },
          { name: 'other', value: 'kosher', label: 'Kosher' }
        ],
        'Service Options': [
          { name: 'other', value: 'eco_friendly', label: 'Eco Friendly' },
          { name: 'other', value: 'local_supplier', label: 'Local Supplier' },
          { name: 'other', value: 'custom_menu', label: 'Custom Menu Available' }
        ]
      }
    }
  };

  useEffect(() => {
    setSearchInput(searchKeywords);
  }, [searchKeywords]);

  const handleSearchKeyDown = (e) => {
    if (e.key === 'Enter') {
      onSearchChange(searchInput);
    }
  };

  const handleSearchBlur = () => {
    onSearchChange(searchInput);
  };

  const toggleDropdown = (dropdownName) => {
    setActiveDropdown(activeDropdown === dropdownName ? null : dropdownName);
  };

  const handleFilterToggle = (filterType, value) => {
    const currentValues = filters[filterType] || [];
    const newValues = currentValues.includes(value)
      ? currentValues.filter(v => v !== value)
      : [...currentValues, value];
    
    onFilterChange(filterType, newValues);
  };

  const isFilterSelected = (filterType, value) => {
    return (filters[filterType] || []).includes(value);
  };

  const renderDropdown = (filterType, config) => {
    const isActive = activeDropdown === filterType;
    
    return (
      <div key={filterType} className="team-order-dropdown">
        <div className={`dropdown dropdown-filter-wrapper dropdown-bubble team-order-filter team-order-filter--${filterType}`}>
          <button 
            className="dropdown-filter-button"
            onClick={() => toggleDropdown(filterType)}
            type="button"
          >
            {config.header}
          </button>
          
          <div className={`filter-content dropdown-content ${isActive ? '' : 'hidden'}`}>
            <ul>
              {Object.entries(config.sections).map(([sectionName, options]) => (
                <div key={sectionName}>
                  <li className="drop-text-header">{sectionName}</li>
                  {options.map((option) => (
                    <li key={`${sectionName}-${option.value}`}>
                      <label className="drop-text">
                        <input
                          type="checkbox"
                          className="checkbox-content"
                          checked={isFilterSelected(filterType, option.value)}
                          onChange={() => handleFilterToggle(filterType, option.value)}
                          data-label={option.label}
                        />
                        <span className="checkbox-content-tick" />
                        {option.label}
                        {option.is_new && <span className="new-filter-tag">NEW</span>}
                      </label>
                    </li>
                  ))}
                </div>
              ))}
            </ul>
          </div>
        </div>
      </div>
    );
  };

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest('.dropdown-filter-wrapper')) {
        setActiveDropdown(null);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  return (
    <div className="supplier-filters-container">
      <div className="team-order-dropdown">
        <input
          className="team-order-filter team-order-filter--search keyword-search-filter"
          placeholder="Search"
          type="search"
          name="filter-team-suppliers"
          value={searchInput}
          onChange={(e) => setSearchInput(e.target.value)}
          onKeyDown={handleSearchKeyDown}
          onBlur={handleSearchBlur}
        />
      </div>
      
      {renderDropdown('category', filterOptions.category)}
      {renderDropdown('other', filterOptions.other)}
    </div>
  );
};

SupplierFilters.propTypes = {
  searchKeywords: PropTypes.string,
  onSearchChange: PropTypes.func.isRequired,
  filters: PropTypes.object.isRequired,
  onFilterChange: PropTypes.func.isRequired
};

SupplierFilters.defaultProps = {
  searchKeywords: ''
};

export default SupplierFilters;
