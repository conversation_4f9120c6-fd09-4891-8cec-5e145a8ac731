import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import Modal from 'react-responsive-modal';

// API utilities

// Utils
import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';
import { getSupplierMenu, getSupplierImageUrl, formatPrice } from './supplierSelectionAPI';

const SupplierMenuModal = ({ supplier, isOpen, onClose, onSelectMenuSections, deliveryDate, suburbId }) => {
  const [menuData, setMenuData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [selectedMenuSections, setSelectedMenuSections] = useState([]);

  useEffect(() => {
    if (isOpen && supplier) {
      fetchSupplierMenu();
    }
  }, [isOpen, supplier]);

  const fetchSupplierMenu = async () => {
    setLoading(true);
    const result = await getSupplierMenu(supplier.slug, deliveryDate, suburbId);

    if (result.success) {
      setMenuData(result.data);
    } else {
      toast.error(result.error, defaultToastOptions);
    }
    setLoading(false);
  };

  const handleMenuSectionToggle = (sectionId) => {
    setSelectedMenuSections((prev) => {
      if (prev.includes(sectionId)) {
        return prev.filter((id) => id !== sectionId);
      }
      return [...prev, sectionId];
    });
  };

  const handleSelectSupplier = () => {
    onSelectMenuSections(selectedMenuSections);
  };

  const getRatingClass = (rating) => (rating >= 3 ? 'team-supplier-rating--approval' : '');

  return (
    <Modal
      isOpen={isOpen}
      onRequestClose={onClose}
      className="supplier-menu-modal"
      overlayClassName="supplier-menu-modal-overlay"
      ariaHideApp={false}
    >
      <div className="modal-content">
        <div className="modal-header">
          <button className="modal-close-btn" onClick={onClose} aria-label="Close modal">
            ×
          </button>
        </div>

        {loading ? (
          <div className="modal-loading">
            <div className="loading-spinner">Loading menu...</div>
          </div>
        ) : (
          <div className="modal-body">
            {/* Supplier banner */}
            <div className="team-supplier-banner">
              <div className="team-supplier-banner__image">
                <img src={getSupplierImageUrl(supplier.image_id)} alt={supplier.company_name || supplier.name} />
              </div>
              <div className="team-supplier-banner__details">
                <h3 className="team-supplier-banner__title">{supplier.company_name || supplier.name}</h3>
                <div className="team-supplier-banner__info">
                  <div>
                    <p className={`team-supplier-rating ${getRatingClass(supplier.rating)}`}>
                      {supplier.rating > 0 ? supplier.rating : '-'}
                    </p>
                    <p>Min Order: {formatPrice(supplier.minimum_spend)}</p>
                    <p>Best for teams of {supplier.best_for_team_size || '5'}+</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Menu sections */}
            {menuData?.menu_sections && (
              <div className="menu-sections">
                <h4>Select Menu Sections</h4>
                <p className="menu-instructions">
                  Choose which sections of the menu you'd like to make available to your team members.
                </p>

                <div className="menu-sections-list">
                  {menuData.menu_sections.map((section) => (
                    <div key={section.id} className="menu-section-item">
                      <label className="menu-section-checkbox">
                        <input
                          type="checkbox"
                          checked={selectedMenuSections.includes(section.id)}
                          onChange={() => handleMenuSectionToggle(section.id)}
                        />
                        <span className="checkmark" />
                        <div className="menu-section-info">
                          <h5>{section.name}</h5>
                          {section.description && <p className="menu-section-description">{section.description}</p>}
                          {section.item_count && <span className="menu-section-count">{section.item_count} items</span>}
                        </div>
                      </label>
                    </div>
                  ))}
                </div>

                {menuData.menu_sections.length === 0 && (
                  <div className="no-menu-sections">
                    <p>No menu sections available for this supplier.</p>
                  </div>
                )}
              </div>
            )}

            {/* Action buttons */}
            <div className="modal-actions">
              <button className="button gray-btn" onClick={onClose}>
                Cancel
              </button>
              <button
                className="button select-supplier-btn"
                onClick={handleSelectSupplier}
                disabled={selectedMenuSections.length === 0}
              >
                Select Supplier
              </button>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};

SupplierMenuModal.propTypes = {
  supplier: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    slug: PropTypes.string.isRequired,
    name: PropTypes.string,
    company_name: PropTypes.string,
    rating: PropTypes.number,
    minimum_spend: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    image_id: PropTypes.string,
    best_for_team_size: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  }).isRequired,
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onSelectMenuSections: PropTypes.func.isRequired,
  deliveryDate: PropTypes.string.isRequired,
  suburbId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
};

export default SupplierMenuModal;
