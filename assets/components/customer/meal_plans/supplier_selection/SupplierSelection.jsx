import { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';

// Components
import SupplierFilters from './SupplierFilters';
import SupplierList from './SupplierList';
import MultiDaySelector from './MultiDaySelector';
import SupplierMenuModal from './SupplierMenuModal';

// API utilities
import {
  fetchSuppliers,
  checkSupplierAvailability,
  checkSupplierCutoffHours,
  validateSupplierSelection,
  formatSelectedSuppliersForSubmission,
  getDeliveryDateForDay
} from './supplierSelectionAPI';

// Utils
import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';

const WEEKDAYS = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'];

const SupplierSelection = ({ 
  mealPlan, 
  setMealPlan, 
  suburbId, 
  budget = 10,
  onSuppliersChange 
}) => {
  // State management
  const [suppliers, setSuppliers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedSuppliers, setSelectedSuppliers] = useState({});
  const [activeDay, setActiveDay] = useState('Mon');
  const [searchKeywords, setSearchKeywords] = useState('');
  const [filters, setFilters] = useState({
    category: [],
    dietary: [],
    delivery: [],
    other: []
  });
  const [supplierAvailability, setSupplierAvailability] = useState({});
  const [menuModalOpen, setMenuModalOpen] = useState(false);
  const [selectedSupplierForMenu, setSelectedSupplierForMenu] = useState(null);

  // Initialize selected suppliers for recurring days
  useEffect(() => {
    if (mealPlan.is_recurring && mealPlan.recurring_days?.length > 0) {
      const initialSelectedSuppliers = {};
      mealPlan.recurring_days.forEach(day => {
        initialSelectedSuppliers[day] = {};
      });
      setSelectedSuppliers(initialSelectedSuppliers);
    }
  }, [mealPlan.is_recurring, mealPlan.recurring_days]);

  // Fetch suppliers when filters change or component mounts
  const fetchSuppliersData = useCallback(async () => {
    if (!suburbId) return;

    setLoading(true);
    const params = {
      suburb_id: suburbId,
      budget: budget,
      search_keywords: searchKeywords,
      ...filters
    };

    const result = await fetchSuppliers(params);
    if (result.success) {
      setSuppliers(result.data.suppliers || []);

      // Check availability for all suppliers
      if (result.data.suppliers?.length > 0) {
        const supplierIds = result.data.suppliers.map(s => s.id);
        await checkAvailabilityForSuppliers(supplierIds);
      }
    } else {
      toast.error(result.error, defaultToastOptions);
    }
    setLoading(false);
  }, [suburbId, budget, searchKeywords, filters]);

  useEffect(() => {
    fetchSuppliersData();
  }, [fetchSuppliersData]);

  // Check supplier availability for multiple suppliers
  const checkAvailabilityForSuppliers = useCallback(async (supplierIds) => {
    if (!supplierIds.length || !suburbId) return;

    const deliveryDate = mealPlan.is_recurring
      ? getDeliveryDateForDay(activeDay, mealPlan.delivery_time)
      : mealPlan.delivery_time;

    const result = await checkSupplierAvailability(supplierIds, deliveryDate, suburbId);
    if (result.success) {
      setSupplierAvailability(prev => ({
        ...prev,
        [deliveryDate]: result.data
      }));
    }
  }, [suburbId, activeDay, mealPlan.is_recurring, mealPlan.delivery_time]);

  // Handle supplier selection
  const handleSupplierSelect = async (supplier) => {
    const currentDay = mealPlan.is_recurring ? activeDay : 'single';
    const deliveryDate = mealPlan.is_recurring
      ? getDeliveryDateForDay(activeDay, mealPlan.delivery_time)
      : mealPlan.delivery_time;

    // Check if supplier is already selected
    const isSelected = selectedSuppliers[currentDay]?.[supplier.id];

    if (isSelected) {
      // Unselect supplier
      setSelectedSuppliers(prev => {
        const updated = { ...prev };
        if (updated[currentDay]) {
          delete updated[currentDay][supplier.id];
        }
        return updated;
      });
    } else {
      // Check cutoff time before selecting
      const cutoffResult = await checkSupplierCutoffHours(supplier.id, deliveryDate, suburbId);

      if (cutoffResult.success && cutoffResult.data.hours_remaining > 0) {
        // Show menu selection modal for package/recurring orders
        if (mealPlan.is_recurring) {
          setSelectedSupplierForMenu(supplier);
          setMenuModalOpen(true);
        } else {
          // Direct selection for simple orders
          selectSupplier(supplier, []);
        }

        // Show success message with cutoff info
        toast.success(
          `You've selected ${cutoffResult.data.supplier_name}. You have ${cutoffResult.data.hours_remaining_message} to get the team order in.`,
          defaultToastOptions
        );
      } else {
        toast.error('Team order lead time has passed for this supplier, choose another supplier.', defaultToastOptions);
      }
    }
  };

  // Select supplier with menu sections
  const selectSupplier = (supplier, selectedMenuSections = []) => {
    const currentDay = mealPlan.is_recurring ? activeDay : 'single';

    setSelectedSuppliers(prev => {
      const updated = {
        ...prev,
        [currentDay]: {
          ...prev[currentDay],
          [supplier.id]: selectedMenuSections
        }
      };

      // Notify parent component with updated data
      if (onSuppliersChange) {
        const formattedData = formatSelectedSuppliersForSubmission(updated, mealPlan);
        onSuppliersChange(formattedData);
      }

      return updated;
    });
  };

  // Handle search
  const handleSearch = (keywords) => {
    setSearchKeywords(keywords);
  };

  // Handle filter changes
  const handleFilterChange = (filterType, values) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: values
    }));
  };

  // Handle day selection for recurring orders
  const handleDaySelect = (day) => {
    setActiveDay(day);

    // Check availability for suppliers when switching days
    if (suppliers.length > 0) {
      const supplierIds = suppliers.map(s => s.id);
      checkAvailabilityForSuppliers(supplierIds);
    }
  };

  // Validate current selection
  const validateSelection = () => {
    return validateSupplierSelection(selectedSuppliers, mealPlan.recurring_days);
  };

  return (
    <div className="supplier-selection-container">
      <div className="supplier-selection-header">
        <h6>
          Select Caterer
          {mealPlan.is_recurring && (
            <span className="multi-supplier-selection-header">
              {' '}for each delivery day
            </span>
          )}
        </h6>
        
        <SupplierFilters
          searchKeywords={searchKeywords}
          onSearchChange={handleSearch}
          filters={filters}
          onFilterChange={handleFilterChange}
        />
      </div>

      {mealPlan.is_recurring && mealPlan.recurring_days?.length > 0 && (
        <MultiDaySelector
          days={mealPlan.recurring_days}
          activeDay={activeDay}
          onDaySelect={handleDaySelect}
          selectedSuppliers={selectedSuppliers}
        />
      )}

      <SupplierList
        suppliers={suppliers}
        loading={loading}
        selectedSuppliers={selectedSuppliers[mealPlan.is_recurring ? activeDay : 'single'] || {}}
        onSupplierSelect={handleSupplierSelect}
        budget={budget}
        supplierAvailability={supplierAvailability}
      />

      {menuModalOpen && selectedSupplierForMenu && (
        <SupplierMenuModal
          supplier={selectedSupplierForMenu}
          isOpen={menuModalOpen}
          onClose={() => {
            setMenuModalOpen(false);
            setSelectedSupplierForMenu(null);
          }}
          onSelectMenuSections={(menuSections) => {
            selectSupplier(selectedSupplierForMenu, menuSections);
            setMenuModalOpen(false);
            setSelectedSupplierForMenu(null);
          }}
          deliveryDate={mealPlan.is_recurring ? getDeliveryDateForDay(activeDay, mealPlan.delivery_time) : mealPlan.delivery_time}
          suburbId={suburbId}
        />
      )}
    </div>
  );
};

SupplierSelection.propTypes = {
  mealPlan: PropTypes.object.isRequired,
  setMealPlan: PropTypes.func.isRequired,
  suburbId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  budget: PropTypes.number,
  onSuppliersChange: PropTypes.func
};

export default SupplierSelection;
