import PropTypes from 'prop-types';
import SupplierCard from './SupplierCard';

const SupplierList = ({ 
  suppliers, 
  loading, 
  selectedSuppliers, 
  onSupplierSelect, 
  budget,
  supplierAvailability 
}) => {
  if (loading) {
    return (
      <div className="form-content has-small-gutter">
        <div className="team-order-list">
          <ul className="team-suppliers-list team-order-list__table row">
            <li>
              <h3 className="mt-2 text-center">Loading Suppliers...</h3>
            </li>
          </ul>
        </div>
      </div>
    );
  }

  if (!suppliers || suppliers.length === 0) {
    return (
      <div className="form-content has-small-gutter">
        <div className="team-order-list">
          <ul className="team-suppliers-list team-order-list__table row">
            <li>
              <div className="no-suppliers-available">
                <h4>No suppliers available</h4>
                <p>Try adjusting your search criteria or filters to find more suppliers.</p>
              </div>
            </li>
          </ul>
        </div>
      </div>
    );
  }

  return (
    <div className="form-content has-small-gutter">
      <div className="team-order-list">
        {/* Table headers - visible on medium screens and up */}
        <div className="row show-for-medium team-order-list__headings">
          <span className="team-order-list__heading small-4 columns no-gutter">NAME</span>
          <div className="small-12 medium-6 columns no-gutter">
            <span className="team-order-list__heading small-3 columns no-gutter">MIN ORDER</span>
            <span className="team-order-list__heading small-2 columns no-gutter">RATING</span>
            <span className="team-order-list__heading small-4 columns no-gutter">LEAD TIME</span>
            <span 
              className="team-order-list__heading small-3 columns no-gutter best-for-header"
              title={`Based on budget of: $${budget} (per head)`}
            >
              BEST FOR
              <sup>*</sup>
            </span>
          </div>
          <div className="small-12 columns no-gutter medium-2">
            &nbsp;
          </div>
        </div>

        {/* Suppliers list */}
        <ul className="team-suppliers-list team-order-list__table row">
          {suppliers.map((supplier) => (
            <SupplierCard
              key={supplier.id}
              supplier={supplier}
              isSelected={!!selectedSuppliers[supplier.id]}
              onSelect={() => onSupplierSelect(supplier)}
              budget={budget}
              isAvailable={!supplierAvailability[supplier.id]?.unavailable}
            />
          ))}
        </ul>
      </div>
    </div>
  );
};

SupplierList.propTypes = {
  suppliers: PropTypes.array.isRequired,
  loading: PropTypes.bool,
  selectedSuppliers: PropTypes.object,
  onSupplierSelect: PropTypes.func.isRequired,
  budget: PropTypes.number,
  supplierAvailability: PropTypes.object
};

SupplierList.defaultProps = {
  loading: false,
  selectedSuppliers: {},
  budget: 10,
  supplierAvailability: {}
};

export default SupplierList;
