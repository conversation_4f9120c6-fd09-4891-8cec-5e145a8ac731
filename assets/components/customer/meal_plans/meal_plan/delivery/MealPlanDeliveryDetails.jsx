import { useContext, useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';

import mealPlansStore from 'store/mealPlansStore';
import adminContext from 'contexts/adminContext';

// components
import DatePicker from 'react-datepicker';
import ReactTooltip from 'react-tooltip';

// error messages
import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';
import SupplierSelection from '../../supplier_selection/SupplierSelection';
import AddressModal from './AddressModal';

const requiredPlanFields = {
  name: 'Name',
  delivery_time: 'Delivery Time',
  delivery_address: 'Delivery Address',
  delivery_suburb_id: 'Delivery Address',
  number_of_people: 'Number of People',
};

const validReminderFrequencies = {
  weekly: 'Weekly',
  monthly: 'Monthly',
};

const MealPlanDeliveryDetails = ({ mealPlan, setMealPlan, isNewPlan, setPanel, setOpenForm }) => {
  const [addressModalOpen, setAddressModalOpen] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});
  const [selectedSuppliers, setSelectedSuppliers] = useState({});
  const hasMealPlans = mealPlansStore((state) => state.hasMealPlans);
  const { isAdmin } = useContext(adminContext);

  const WEEKDAYS = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'];

  const handleChange = (event) => {
    const { name, value } = event.target;
    setMealPlan((state) => ({ ...state, [name]: value }));
    setValidationErrors((errors) => ({ ...errors, [name]: false })); // Clear error on change
  };

  const handleToggleRecurring = () => {
    setMealPlan((state) => ({
      ...state,
      is_recurring: !state.is_recurring,
      recurring_days: !state.is_recurring ? [] : state.recurring_days || [],
    }));
    setValidationErrors((errors) => ({ ...errors, is_recurring: false }));
  };

  const handleDayToggle = (day) => {
    setMealPlan((state) => {
      const currentDays = state.recurring_days || [];
      const isSelected = currentDays.includes(day);

      const newDays = isSelected ? currentDays.filter((d) => d !== day) : [...currentDays, day];

      return { ...state, recurring_days: newDays };
    });
  };

  const handleSuppliersChange = (suppliersData) => {
    setSelectedSuppliers(suppliersData);
    // Store supplier data in meal plan for submission
    setMealPlan((state) => ({
      ...state,
      selected_suppliers: suppliersData,
    }));
  };

  const handleDateChange = (datetime) => {
    const formattedDate = moment(datetime).format('YYYY-MM-DD h:mm a');
    setMealPlan((state) => ({ ...state, delivery_time: formattedDate }));
    setValidationErrors((errors) => ({ ...errors, delivery_time: false })); // Clear error on change
  };

  // Clear delivery address validation error when address is set via the modal
  useEffect(() => {
    if (mealPlan.delivery_address) {
      setValidationErrors((errors) => ({ ...errors, delivery_address: false }));
    }
  }, [mealPlan.delivery_address]);

  const validateForm = () => {
    const errors = {};
    Object.keys(requiredPlanFields).forEach((field) => {
      const isMissing = field === 'number_of_people' ? !mealPlan[field] || mealPlan[field] < 0 : !mealPlan[field];
      if (isMissing) errors[field] = true;
    });
    setValidationErrors(errors);
    if (Object.keys(errors).length) {
      toast.error('Form has missing fields', { ...defaultToastOptions, autoClose: 5000 });
    }
    return Object.keys(errors).length === 0;
  };

  const handleSetPaymentDetails = () => {
    if (validateForm()) {
      setPanel('payment');
    }
  };

  return (
    <div className="authorization-module plan" style={isNewPlan ? {} : { borderRight: '1px solid #e0e0e0' }}>
      <div className="form-section">
        <h3 className="section-heading">Delivery Details</h3>
      </div>

      <div className="form-section">
        <label className="name">Name</label>
        <input
          type="text"
          name="name"
          className={`form-input ${validationErrors.name ? 'error' : ''}`}
          onChange={handleChange}
          value={mealPlan.name}
          placeholder="Name of your meal plan"
        />
      </div>
      <div style={{ display: 'flex' }}>
        <div className="form-section" style={{ flex: 1, marginRight: '8px' }}>
          <label className="staff">
            {mealPlan.kind === 'shared' ? 'Number of People' : 'Estimated Number of People'}
          </label>
          <input
            type="number"
            name="number_of_people"
            className={`form-input ${validationErrors.number_of_people ? 'error' : ''}`}
            onChange={handleChange}
            value={mealPlan.number_of_people}
            placeholder={mealPlan.kind === 'shared' ? 'Number of People' : 'Estimated Number of People'}
          />
        </div>
        <div className="form-section" style={{ flex: 1 }}>
          <label className="time">Delivery Time</label>
          <DatePicker
            selected={mealPlan.delivery_time ? moment(mealPlan.delivery_time, 'YYYY-MM-DD h:mm a').toDate() : null}
            onChange={handleDateChange}
            showTimeSelect
            showTimeSelectOnly
            timeIntervals={15}
            minDate={new Date()}
            name="delivery_time"
            dateFormat="h:mm aa"
            autoComplete="off"
            className={`form-input time-container ${validationErrors.delivery_time ? 'error' : ''}`}
            placeholderText="Time of your meal plan"
          />
        </div>
      </div>

      <div className="form-section">
        <label className="address">Delivery Address</label>
        <input
          type="text"
          name="delivery_address"
          className={`form-input clickable-readonly ${
            validationErrors.delivery_address || validationErrors.delivery_suburb_id ? 'error' : ''
          }`}
          value={mealPlan.delivery_address ? `${mealPlan.delivery_address}, ${mealPlan.suburb_label}` : ''}
          onClick={() => setAddressModalOpen(true)}
          placeholder="Delivery Address"
          readOnly
        />
        {!!addressModalOpen && <AddressModal setMealPlan={setMealPlan} setModalOpen={setAddressModalOpen} />}
      </div>

      <div className="form-section">
        <label className="level">Delivery Address Level</label>
        <input
          type="text"
          name="delivery_address_level"
          className="form-input"
          onChange={handleChange}
          value={mealPlan.delivery_address_level}
          placeholder="Delivery Address Level"
        />
      </div>

      <div className="form-section">
        <label className="instructions">Delivery Instructions</label>
        <textarea
          rows={3}
          name="delivery_instruction"
          className="form-input"
          onChange={handleChange}
          value={mealPlan.delivery_instruction}
          placeholder="Delivery Instructions"
        />
      </div>

      {mealPlan.kind === 'individual' && (
        <div className="form-section">
          <label className="time">
            Set to be recurring Meal Plan
            <span
              className="ml-1-2 whats-this bottom tooltip-dash"
              data-tip
              data-for={`meal-plan-${mealPlan.id}-recurring-info`}
            >
              What's This?
            </span>
            <ReactTooltip id={`meal-plan-${mealPlan.id}-recurring-info`} place="right" effect="solid">
              <small>
                Select this if you'd like to set up a recurring order for a day or days of the week. You can choose
                different suppliers
              </small>
            </ReactTooltip>
          </label>
          <div className="section-toggle">
            <input type="checkbox" name="is_recurring" checked={mealPlan.is_recurring} readOnly />
            <span className="section-toggle__switch" onClick={handleToggleRecurring} />
          </div>
        </div>
      )}

      {mealPlan.is_recurring && (
        <div className="form-section">
          <label className="time">Select Days</label>
          <div style={{ display: 'flex', gap: '8px', width: '100%' }}>
            {WEEKDAYS.map((day) => {
              const isSelected = (mealPlan.recurring_days || []).includes(day);
              return (
                <label
                  key={day}
                  className={`recurring-day-meal-plan ${isSelected ? 'selected' : ''}`}
                  onClick={(e) => {
                    e.preventDefault();
                    handleDayToggle(day);
                  }}
                >
                  <input
                    type="checkbox"
                    name="recurring_days"
                    value={day}
                    checked={isSelected}
                    readOnly
                    style={{ display: 'none' }}
                  />
                  {day}
                </label>
              );
            })}
          </div>
        </div>
      )}

      {/* Supplier Selection for Recurring Orders */}
      {mealPlan.is_recurring && mealPlan.recurring_days?.length > 0 && (
        <div className="form-section">
          <label className="supplier-selection">Supplier Selection</label>
          <p className="form-help-text">
            Choose suppliers for each day of your recurring meal plan. You can select different suppliers for different
            days.
          </p>
          <SupplierSelection
            mealPlan={mealPlan}
            setMealPlan={setMealPlan}
            suburbId={mealPlan.delivery_suburb_id}
            budget={mealPlan.budget_per_person}
            onSuppliersChange={handleSuppliersChange}
          />
        </div>
      )}

      <div className="form-section">
        <label className="time">
          Order Reminders
          <span
            className="ml-1-2 whats-this bottom tooltip-dash"
            data-tip
            data-for={`meal-plan-${mealPlan.id}-reminder-info`}
          >
            What's This?
          </span>
          <ReactTooltip id={`meal-plan-${mealPlan.id}-reminder-info`} place="right" effect="solid">
            <small>How often you need reminders for placing orders.</small>
          </ReactTooltip>
        </label>
        <select name="reminder_frequency" onChange={handleChange} className="form-input">
          <option key="no-reminder-frequency" value="" selected={!mealPlan.reminder_frequency}>
            Do Not Notify
          </option>
          {Object.keys(validReminderFrequencies).map((frequency) => (
            <option
              key={`reminder-frequency-${frequency}`}
              value={frequency}
              selected={mealPlan.reminder_frequency === frequency}
            >
              {validReminderFrequencies[frequency]}
            </option>
          ))}
        </select>
      </div>

      {isAdmin && (
        <div className="form-section">
          <label className="name">Admin Notes</label>
          <textarea
            name="admin_notes"
            className="form-input"
            value={mealPlan.admin_notes}
            onChange={handleChange}
            placeholder="e.g. At least 2 servings of Vegan items required."
          />
        </div>
      )}

      {isNewPlan && (
        <div className="form-section" style={{ marginTop: '2rem' }}>
          <a className="button" onClick={handleSetPaymentDetails} style={{ width: '100%', background: 'black' }}>
            Set Payment Details
          </a>
          {hasMealPlans && (
            <a className="button gray-btn" onClick={() => setOpenForm(false)} style={{ width: '100%' }}>
              Cancel
            </a>
          )}
        </div>
      )}
    </div>
  );
};

MealPlanDeliveryDetails.propTypes = {
  mealPlan: PropTypes.object.isRequired,
  setMealPlan: PropTypes.func.isRequired,
  isNewPlan: PropTypes.bool.isRequired,
  setPanel: PropTypes.func.isRequired,
  setOpenForm: PropTypes.func.isRequired,
};

export default MealPlanDeliveryDetails;
