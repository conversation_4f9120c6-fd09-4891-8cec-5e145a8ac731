require 'rails_helper'

RSpec.describe TeamOrder<PERSON>ttendee, type: :model, team_orders: true do

  context 'validations' do
    it { is_expected.to validate_presence_of(:uniq_code) }
    it { is_expected.to validate_uniqueness_of(:uniq_code) }
    it { is_expected.to validate_inclusion_of(:status).in_array(TeamOrderAttendee::VALID_ATTENDEE_STATUSES) }
    it 'has a valid factory' do
      factory_attendee = build(:team_order_attendee, :random)
      expect(factory_attendee).to be_valid
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:order) }
    it { is_expected.to have_one(:meal_plan) } # through order
    it { is_expected.to belong_to(:event_attendee) }
    it { is_expected.to belong_to(:level) } # TeamOrder::Level
  end

  context 'instance methods' do
    let!(:team_order) { create(:order, :draft, status: 'pending', order_variant: 'team_order', unique_event_id: SecureRandom.hex(7)) }
    let!(:team_admin_attendee) { create(:team_order_attendee, :random, order: team_order, uniq_code: team_order.unique_event_id) }
    let!(:team_order_attendee) { create(:team_order_attendee, :random, order: team_order) }

    describe '.is_team_admin?' do
      it 'returns true if the attendee uniq code is similar to the associated team order\'s unique_event_id' do
        expect(team_admin_attendee.is_team_admin?).to be_truthy
      end

      it 'returns false if the attendee uniq code is different to the associated team order\'s unique_event_id' do
        expect(team_order_attendee.is_team_admin?).to be_falsey
      end
    end

    describe '.meal_plan_attendee' do
      let!(:meal_plan) { create(:meal_plan, :random, :team_meal_plan) }
      let!(:meal_plan_attendee) { create(:meal_plan_attendee, :random, meal_plan: meal_plan, event_attendee: team_admin_attendee.event_attendee) }

      before do
        team_order.update_column(:meal_plan_id, meal_plan.id)
      end

      it 'returns the meal plan attendee with the same event attendee' do
        expect(team_admin_attendee.meal_plan_attendee).to eq(meal_plan_attendee)
      end

      it 'returns blank if the meal plan is not connected to the attendee order' do
        team_order.update_column(:meal_plan_id, nil)

        expect(team_admin_attendee.meal_plan_attendee).to be_blank
      end

      it 'returns blank if the meal plan is not an individual meal plan' do
        meal_plan.update_column(:kind, 'shared')

        expect(team_admin_attendee.meal_plan_attendee).to be_blank
      end
    end
  end

end
