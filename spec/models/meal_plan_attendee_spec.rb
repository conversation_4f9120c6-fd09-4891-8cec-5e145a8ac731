require 'rails_helper'

RSpec.describe MealPlanAttendee, type: :model do
  
  context 'validations' do
    it { is_expected.to validate_presence_of(:meal_plan) }
    it { is_expected.to validate_presence_of(:event_attendee) }
    it { is_expected.to validate_presence_of(:uuid) }
    it { is_expected.to validate_presence_of(:status) }
    it { is_expected.to validate_inclusion_of(:status).in_array(MealPlanAttendee::VALID_ATTENDEE_STATUSES) }

    it 'has a valid factory' do
      meal_plan_attendee = build(:meal_plan_attendee, :random)

      expect(meal_plan_attendee).to be_valid
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:meal_plan) }
    it { is_expected.to belong_to(:event_attendee) }
  end
end
