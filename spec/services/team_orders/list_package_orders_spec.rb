require 'rails_helper'

RSpec.describe TeamOrders::ListPackageOrders, type: :service, team_orders: true do

  let!(:delivery_start) { Time.zone.parse('2022-08-01').beginning_of_month + 1.day } # Setting Time for time based scoping
  let!(:package_id) { SecureRandom.uuid }
  let!(:team_order1) { create(:order, :package_team_order, package_id: package_id, name: 'team order1', delivery_at: delivery_start + 2.days) }
  let!(:team_order2) { create(:order, :package_team_order, package_id: package_id, name: 'team order2', delivery_at: delivery_start + 1.days) }
  let!(:team_order3) { create(:order, :package_team_order, package_id: package_id, name: 'team order3', delivery_at: delivery_start + 3.days) }

  it 'lists all linked package orders for the passed in team order' do
    package_orders = TeamOrders::ListPackageOrders.new(team_order: team_order1).call

    expect(package_orders).to include(team_order1, team_order2, team_order3)
  end

  it 'return empty if passed in order is not a package team_order' do
    team_order1.team_order_detail.update_column(:package_id, nil)
    package_orders = TeamOrders::ListPackageOrders.new(team_order: team_order1).call

    expect(package_orders).to be_empty
  end

  it 'filters active team orders' do
    team_order2.update_column(:status, %w[delivered rejected cancelled skipped].sample)
    filter_options = { active_only: true }
    package_orders = TeamOrders::ListPackageOrders.new(team_order: team_order1, options: filter_options).call

    expect(package_orders).to_not include(team_order2)
    expect(package_orders).to include(team_order1, team_order3)
  end

  it 'filters future team orders (based on current time)' do
    team_order3.update_column(:delivery_at, delivery_start - 1.day)
    filter_options = { future_only: true, scoped_time: delivery_start }
    package_orders = TeamOrders::ListPackageOrders.new(team_order: team_order1, options: filter_options).call

    expect(package_orders).to_not include(team_order3)
    expect(package_orders).to include(team_order1, team_order2)
  end

  it 'sorts the orders by delivery datetime (default)' do
    package_orders = TeamOrders::ListPackageOrders.new(team_order: team_order1).call

    expect(package_orders).to eq([team_order2, team_order1, team_order3])
  end

  it 'sorts the orders by the passed in config' do
    filter_options = { order_by: 'name' } # as a field name
    package_orders = TeamOrders::ListPackageOrders.new(team_order: team_order1, options: filter_options).call
    expect(package_orders).to eq([team_order1, team_order2, team_order3])

    filter_options = { order_by: { delivery_at: :desc } } # as a hash with direction
    package_orders = TeamOrders::ListPackageOrders.new(team_order: team_order1, options: filter_options).call
    expect(package_orders).to eq([team_order3, team_order1, team_order2])
  end

  context 'for a team order attendee' do
    let!(:team_order_attendee11) { create(:team_order_attendee, :random, order: team_order1, status: 'invited') }
    let!(:team_order_attendee12) { create(:team_order_attendee, :random, order: team_order2, event_attendee: team_order_attendee11.event_attendee, status: 'invited') }
    let!(:team_order_attendee13) { create(:team_order_attendee, :random, order: team_order3, event_attendee: team_order_attendee11.event_attendee, status: 'invited') }

    let!(:team_order_attendee21) { create(:team_order_attendee, :random, order: team_order1, status: 'invited') }
    let!(:team_order_attendee23) { create(:team_order_attendee, :random, order: team_order3, event_attendee: team_order_attendee21.event_attendee, status: 'invited') }

    let!(:team_order_attendee31) { create(:team_order_attendee, :random, order: team_order1, status: 'invited') }
    let!(:team_order_attendee32) { create(:team_order_attendee, :random, order: team_order2, event_attendee: team_order_attendee31.event_attendee, status: 'invited') }
    let!(:team_order_attendee33) { create(:team_order_attendee, :random, order: team_order3, event_attendee: team_order_attendee31.event_attendee, status: %w[declined cancelled].sample) }

    it 'lists all team orders that the attendee is a part of' do
      filter_options = { for_attendee: team_order_attendee11 }
      package_orders = TeamOrders::ListPackageOrders.new(team_order: team_order1, options: filter_options).call

      expect(package_orders).to include(team_order1, team_order2, team_order3)
    end

    it 'does not list team orders that the attendee is not a part of' do
      filter_options = { for_attendee: team_order_attendee21 }
      package_orders = TeamOrders::ListPackageOrders.new(team_order: team_order1, options: filter_options).call

      expect(package_orders).to_not include(team_order2)
      expect(package_orders).to include(team_order1, team_order3)
    end

    it 'does not list team orders that the attendee has decline or is removed from' do
      filter_options = { for_attendee: team_order_attendee31 }
      package_orders = TeamOrders::ListPackageOrders.new(team_order: team_order1, options: filter_options).call

      expect(package_orders).to_not include(team_order3)
      expect(package_orders).to include(team_order1, team_order2)
    end

    context 'belonging to a recurring team order package' do
      before do
        [team_order1, team_order2, team_order3].each do |team_order|
          team_order.update_column(:order_variant, 'recurring_team_order')
        end
      end

      it 'lists all package orders irrespective of being a member' do
        filter_options = { for_attendee: team_order_attendee21 }
        package_orders = TeamOrders::ListPackageOrders.new(team_order: team_order1, options: filter_options).call

        expect(package_orders).to include(team_order1, team_order2, team_order3)
      end
    end
  end

  it 'filters out self if requested' do
    filter_options = { exclude_self: true }
    package_orders = TeamOrders::ListPackageOrders.new(team_order: team_order1, options: filter_options).call

    expect(package_orders).to include(team_order2, team_order3)
    expect(package_orders).to_not include(team_order1)
  end

  context 'delivery scoped orders' do
    let!(:team_order21) { create(:order, :package_team_order, package_id: package_id, name: 'team order21', delivery_at: delivery_start + 1.week + 2.days) }
    let!(:team_order22) { create(:order, :package_team_order, package_id: package_id, name: 'team order22', delivery_at: delivery_start + 2.weeks + 1.days) }
    let!(:team_order23) { create(:order, :package_team_order, package_id: package_id, name: 'team order23', delivery_at: delivery_start + 1.month + 3.days) }

    it 'lists all orders by default' do
      filter_options = { scoped_to: ['all', 'any_other_scope', nil].sample }
      package_orders = TeamOrders::ListPackageOrders.new(team_order: team_order1, options: filter_options).call

      expect(package_orders).to include(team_order1, team_order2, team_order3, team_order21, team_order22, team_order23)
    end

    it 'lists orders within the recent month of the passed in scoped time' do
      filter_options = { scoped_to: 'recent_month', scoped_time: delivery_start }
      package_orders = TeamOrders::ListPackageOrders.new(team_order: team_order1, options: filter_options).call

      expect(package_orders).to include(team_order1, team_order2, team_order3, team_order21, team_order22)
      expect(package_orders).to_not include(team_order23)
    end

    it 'lists orders within the recent fortnight of the passed in scoped time' do
      filter_options = { scoped_to: 'recent_fortnight', scoped_time: delivery_start }
      package_orders = TeamOrders::ListPackageOrders.new(team_order: team_order1, options: filter_options).call

      expect(package_orders).to include(team_order1, team_order2, team_order3, team_order21)
      expect(package_orders).to_not include(team_order22, team_order23)
    end

    it 'lists orders within the recent week of the passed in scoped time' do
      filter_options = { scoped_to: 'recent_week', scoped_time: delivery_start }
      package_orders = TeamOrders::ListPackageOrders.new(team_order: team_order1, options: filter_options).call

      expect(package_orders).to include(team_order1, team_order2, team_order3)
      expect(package_orders).to_not include(team_order21, team_order22, team_order23)
    end
  end

end
