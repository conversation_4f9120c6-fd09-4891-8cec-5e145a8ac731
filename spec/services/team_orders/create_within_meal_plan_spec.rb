require 'rails_helper'

RSpec.describe TeamOrders::CreateWithinMealPlan, type: :service, orders: true, team_orders: true, meal_plans: true do

  let!(:team_admin) { create(:customer_profile, :random) }
  let!(:team_meal_plan) { create(:meal_plan, :random, :team_meal_plan, customer_profile: team_admin) }

  let!(:supplier) { create(:supplier_profile, :random) }

  let!(:team_order_params) do
    {
      supplier_id: supplier.id,
      delivery_at: (Time.zone.now + rand(10..20).days).to_s,
      selected_menu_sections: [],
    }
  end

  before do
    # mock team admin email sender
    customer_email_sender = delayed_customer_email_sender = double(TeamOrders::Emails::SendAdminNewOrderEmail)
    allow(TeamOrders::Emails::SendAdminNewOrderEmail).to receive(:new).and_return(customer_email_sender)
    allow(customer_email_sender).to receive(:delay).and_return(delayed_customer_email_sender)
    allow(delayed_customer_email_sender).to receive(:call).and_return(true)

    # mock supplier email sender
    supplier_email_sender = delayed_supplier_email_sender = double(Suppliers::Emails::SendTeamOrderHeadsUpEmail)
    allow(Suppliers::Emails::SendTeamOrderHeadsUpEmail).to receive(:new).and_return(supplier_email_sender)
    allow(supplier_email_sender).to receive(:delay).and_return(delayed_supplier_email_sender)
    allow(delayed_supplier_email_sender).to receive(:call).and_return(true)

    # mock event logger
    event_logger = delayed_event_logger = double(EventLogs::Create)
    allow(EventLogs::Create).to receive(:new).and_return(event_logger)
    allow(event_logger).to receive(:delay).and_return(delayed_event_logger)
    allow(delayed_event_logger).to receive(:call).and_return(true)
  end

  it 'create a one-off team order within the passed in meal plan' do
    team_order_creator = TeamOrders::CreateWithinMealPlan.new(meal_plan: team_meal_plan, team_order_params: team_order_params, team_admin: team_admin).call

    expect(team_order_creator).to be_success

    created_team_order = team_order_creator.team_order
    expect(created_team_order).to be_present
    expect(created_team_order).to be_persisted
    expect(created_team_order.order_type).to eq('one-off')
    expect(created_team_order.order_variant).to eq('team_order')
    expect(created_team_order.status).to eq('pending')
    expect(created_team_order.customer_profile).to eq(team_admin)
    expect(created_team_order.meal_plan).to eq(team_meal_plan)
    expect(created_team_order.unique_event_id).to be_present
    expect(created_team_order.uuid).to be_present
  end

  it 'creates the team order with the meal plan order details, delivery details and billing fields' do
    team_order_creator = TeamOrders::CreateWithinMealPlan.new(meal_plan: team_meal_plan, team_order_params: team_order_params, team_admin: team_admin).call

    expect(team_order_creator).to be_success
    created_team_order = team_order_creator.team_order

    TeamOrders::CreateWithinMealPlan::ORDER_FIELDS.each do |field|
      expect(created_team_order.send(field)).to eq(team_meal_plan.send(field))
    end

    TeamOrders::CreateWithinMealPlan::DELIVERY_FIELDS.each do |field|
      expect(created_team_order.send(field)).to eq(team_meal_plan.send(field))
    end

    TeamOrders::CreateWithinMealPlan::BILLING_FIELDS.each do |field|
      expect(created_team_order.send(field)).to eq(team_meal_plan.send(field))
    end
  end

  it 'creates the team order with the combination of the passed in delivery datetime' do
    team_order_creator = TeamOrders::CreateWithinMealPlan.new(meal_plan: team_meal_plan, team_order_params: team_order_params, team_admin: team_admin).call

    expect(team_order_creator).to be_success
    created_team_order = team_order_creator.team_order

    expected_delivery_at = Time.zone.parse(team_order_params[:delivery_at])
    expect(created_team_order.delivery_at).to eq(expected_delivery_at)
  end

  context 'with purchase orders' do
    let!(:gst_po) { create(:customer_purchase_order, :random, customer_profile: team_admin, po_number: 'GST PO') }
    let!(:gst_free_po) { create(:customer_purchase_order, :random, customer_profile: team_admin, po_number: 'GST-Free PO') }

    before do
      team_meal_plan.update_columns(cpo_id: gst_po.id, gst_free_cpo_id: gst_free_po.id)
    end

    it 'attaches the PO to the orders' do
     team_order_creator = TeamOrders::CreateWithinMealPlan.new(meal_plan: team_meal_plan, team_order_params: team_order_params, team_admin: team_admin).call

     expect(team_order_creator).to be_success
     created_team_order = team_order_creator.team_order

     expect(created_team_order.customer_purchase_order).to eq(gst_po)
     expect(created_team_order.gst_free_customer_purchase_order).to eq(gst_free_po)
    end
  end # with purchase orders

  it 'creates the package team order details from passed in meal plan' do
    team_order_creator = TeamOrders::CreateWithinMealPlan.new(meal_plan: team_meal_plan, team_order_params: team_order_params, team_admin: team_admin).call

    expect(team_order_creator).to be_success
    created_team_order = team_order_creator.team_order

    created_team_order_detail = created_team_order.team_order_detail
    expect(created_team_order_detail).to be_present
    expect(created_team_order_detail.budget).to eq(team_meal_plan.budget)
    expect(created_team_order_detail.cutoff_option).to eq(team_meal_plan.cutoff_option)
    expect(created_team_order_detail.package_id).to eq(team_meal_plan.uuid)
  end

  it 'creates a non-package (one-off) team order if is_one_off is set to false' do
    one_off_team_order_params = team_order_params.merge({ is_one_off: true })
    team_order_creator = TeamOrders::CreateWithinMealPlan.new(meal_plan: team_meal_plan, team_order_params: one_off_team_order_params, team_admin: team_admin).call

    expect(team_order_creator).to be_success
    created_team_order = team_order_creator.team_order

    created_team_order_detail = created_team_order.team_order_detail
    expect(created_team_order_detail).to be_present
    expect(created_team_order_detail.package_id).to be_nil
  end

  it 'creates the team order with the passed in supplier' do
    team_order_creator = TeamOrders::CreateWithinMealPlan.new(meal_plan: team_meal_plan, team_order_params: team_order_params, team_admin: team_admin).call

    expect(team_order_creator).to be_success
    created_team_order = team_order_creator.team_order

    team_order_suppliers = created_team_order.order_suppliers
    expect(team_order_suppliers.map(&:supplier_profile)).to include(supplier)

    expect(created_team_order.team_supplier_profiles).to include(supplier)
  end

  context 'with selected_menu_sections' do
    let!(:menu_section1) { create(:menu_section, :random, supplier_profile: supplier) }
    let!(:menu_section2) { create(:menu_section, :random, supplier_profile: supplier) }
    let!(:menu_section3) { create(:menu_section, :random, supplier_profile: supplier) }

    let!(:team_order_params_with_menu_sections) do
      team_order_params.merge({
        selected_menu_sections: [menu_section1, menu_section3].map(&:id)
      })
    end

    it 'sets the team supplier with the selected menu sections' do
      team_order_creator = TeamOrders::CreateWithinMealPlan.new(meal_plan: team_meal_plan, team_order_params: team_order_params_with_menu_sections, team_admin: team_admin).call

      expect(team_order_creator).to be_success
      created_team_order = team_order_creator.team_order

      team_order_supplier = created_team_order.order_suppliers.where(supplier_profile: supplier).first
      expect(team_order_supplier).to be_present
      expect(team_order_supplier.selected_menu_sections).to include(*[menu_section1, menu_section3].map(&:id))
      expect(team_order_supplier.selected_menu_sections).to_not include(menu_section2.id) # not selected
    end

    it 'only sets the team supplier with the selected suppliers selected menu sections' do
      supplier2 = create(:supplier_profile, :random)
      menu_section1.update_column(:supplier_profile_id, supplier2.id)

      team_order_creator = TeamOrders::CreateWithinMealPlan.new(meal_plan: team_meal_plan, team_order_params: team_order_params_with_menu_sections, team_admin: team_admin).call
      expect(team_order_creator).to be_success
      created_team_order = team_order_creator.team_order

      team_order_supplier = created_team_order.order_suppliers.where(supplier_profile: supplier).first
      expect(team_order_supplier).to be_present
      expect(team_order_supplier.selected_menu_sections).to include(menu_section3.id)
      expect(team_order_supplier.selected_menu_sections).to_not include(menu_section1.id) # not part of supplier
      expect(team_order_supplier.selected_menu_sections).to_not include(menu_section2.id) # not selected
    end
  end

  it 'notifies both the team admin (customer) and supplier' do
    expect(TeamOrders::Emails::SendAdminNewOrderEmail).to receive(:new).with(team_order: anything) # created team order
    expect(Suppliers::Emails::SendTeamOrderHeadsUpEmail).to receive(:new).with(team_order: anything, supplier: supplier)

    team_order_creator = TeamOrders::CreateWithinMealPlan.new(meal_plan: team_meal_plan, team_order_params: team_order_params, team_admin: team_admin).call
    expect(team_order_creator).to be_success
  end

  it 'logs a `New Order Created` event', event_logs: true do
    expect(EventLogs::Create).to receive(:new).with(event_object: anything, event: 'new-team-order-created') # event object is created team order

    team_order_creator = TeamOrders::CreateWithinMealPlan.new(meal_plan: team_meal_plan, team_order_params: team_order_params, team_admin: team_admin).call
    expect(team_order_creator).to be_success
  end
end
