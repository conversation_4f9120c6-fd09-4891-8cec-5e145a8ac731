require 'rails_helper'

RSpec.describe TeamOrderAttendees::List, type: :service, team_order: true, attendees: true do

  let!(:contact1) { create(:event_attendee, :random, first_name: 'name1', last_name: 'last1') }
  let!(:contact2) { create(:event_attendee, :random, first_name: 'name2', last_name: 'last2') }
  let!(:contact3) { create(:event_attendee, :random, first_name: 'name3', last_name: 'last3') }
  let!(:contact4) { create(:event_attendee, :random, first_name: 'name4', last_name: 'last4') }

  let!(:team_order1) { create(:order, :team_order) }
  let!(:team_order2) { create(:order, :team_order) }
  let!(:team_order3) { create(:order, :team_order) }

  let!(:attendee11) { create(:team_order_attendee, :random, event_attendee: contact1, order: team_order1, status: 'invited') }
  let!(:attendee12) { create(:team_order_attendee, :random, event_attendee: contact2, order: team_order1, status: 'ordered') }

  let!(:attendee21) { create(:team_order_attendee, :random, event_attendee: contact3, order: team_order2, status: 'ordered') }
  let!(:attendee22) { create(:team_order_attendee, :random, event_attendee: contact4, order: team_order2, status: 'declined') }

  let!(:attendee31) { create(:team_order_attendee, :random, event_attendee: contact2, order: team_order3, status: 'declined') }
  let!(:attendee32) { create(:team_order_attendee, :random, event_attendee: contact3, order: team_order3, status: 'invited') }

  it 'lists all team order attendees by default' do
    attendees = TeamOrderAttendees::List.new.call

    expect(attendees).to include(attendee11, attendee12, attendee21, attendee22, attendee31, attendee32)
  end

  it 'filters the team order attendees by a single order' do
    lister_options = { order: team_order1 }
    attendees = TeamOrderAttendees::List.new(options: lister_options).call

    expect(attendees).to include(attendee11, attendee12)
    expect(attendees).to_not include(attendee21, attendee22, attendee31, attendee32)
  end

  it 'filters the team order attendees by multiple orders' do
    lister_options = { orders: [team_order2, team_order3] }
    attendees = TeamOrderAttendees::List.new(options: lister_options).call

    expect(attendees).to include(attendee21, attendee22, attendee31, attendee32)
    expect(attendees).to_not include(attendee11, attendee12)
  end

  it 'filters non-active (declined/cancelled) team order attendees' do
    lister_options = { active_only: true }
    attendees = TeamOrderAttendees::List.new(options: lister_options).call

    expect(attendees).to include(attendee11, attendee12, attendee21, attendee32)
    expect(attendees).to_not include(attendee22, attendee31)
  end

  it 'sorts the attendees by their name' do
    lister_options = { sort_by: 'contact_name' }
    attendees = TeamOrderAttendees::List.new(options: lister_options).call

    expect(attendees.size).to eq(6)

    expect(attendees[0]).to eq(attendee11)
    expect(attendees[1]).to eq(attendee12)
    expect(attendees[2]).to eq(attendee31)
    expect(attendees[3]).to eq(attendee21)
    expect(attendees[4]).to eq(attendee32)
    expect(attendees[5]).to eq(attendee22)
  end

  context 'include admin as attendee' do
    let!(:admin_attendee) { double(TeamOrderAttendee) }

    before do
      attendee_fetcher = double(TeamOrderAttendees::Fetch)
      allow(TeamOrderAttendees::Fetch).to receive(:new).and_return(attendee_fetcher)
      allow(attendee_fetcher).to receive(:call).and_return(admin_attendee)
    end

    it 'adds the admin as an attendee to the top of the list' do
      lister_options = { order: team_order1, include_admin: team_order1.customer_profile }

      attendees = TeamOrderAttendees::List.new(options: lister_options).call

      expect(attendees).to include(admin_attendee, attendee11, attendee12)
      expect(attendees).to_not include(attendee21, attendee22, attendee31, attendee32) # not belonging to team_order1

      expect(attendees.first).to eq(admin_attendee)
    end
  end

  context 'package attendees' do
    let!(:package_id) { SecureRandom.uuid }
    before do
      [team_order1, team_order3].each do |order|
        order.team_order_detail.update_column(:package_id, package_id)
      end
    end

    it 'includes non-duplicate package attendees' do
      lister_options = { order: team_order1, include_package_attendees: true }

      attendees = TeamOrderAttendees::List.new(options: lister_options).call

      expect(attendees).to include(attendee11, attendee12, attendee32)
      expect(attendees.map(&:event_attendee)).to include(contact1, contact2)
      expect(attendees.map(&:event_attendee)).to include(contact3) # from team_order3 (part of same package as team_order1)

      expect(attendees).to_not include(attendee21, attendee22) # not belonging to team_order1
      expect(attendees).to_not include(attendee31) # duplicate
    end

    it 'does not include cancelled team order attendees' do
      attendee32.update_column(:status, 'cancelled')
      lister_options = { order: team_order1, include_package_attendees: true }

      attendees = TeamOrderAttendees::List.new(options: lister_options).call

      expect(attendees).to_not include(attendee32)
    end

    it 'does not include team order attendees if in-active event_attendee (contact)' do
      contact3.update_column(:active, false)
      lister_options = { order: team_order1, include_package_attendees: true }

      attendees = TeamOrderAttendees::List.new(options: lister_options).call

      expect(attendees).to_not include(attendee32)
    end
  end # package attendees

end