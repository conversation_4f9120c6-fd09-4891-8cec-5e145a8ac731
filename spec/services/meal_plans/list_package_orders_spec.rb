require 'rails_helper'

RSpec.describe MealPlans::ListPackageOrders, type: :service, meal_plans: true, team_orders: true, orders: true do
    
  let!(:delivery_start) { Time.zone.parse('2022-08-01').beginning_of_month + 1.day } # Setting Time for time based scoping  

  let!(:customer) { create(:customer_profile, :random) }
  let!(:meal_plan) { create(:meal_plan, :random, :team_meal_plan, customer_profile: customer) }  

  let!(:team_order1) { create(:order, :package_team_order, meal_plan: meal_plan, package_id: meal_plan.uuid, name: 'team order1', delivery_at: delivery_start + 2.days) }
  let!(:team_order2) { create(:order, :package_team_order, meal_plan: meal_plan, package_id: meal_plan.uuid, name: 'team order2', delivery_at: delivery_start + 1.days) }
  let!(:team_order3) { create(:order, :package_team_order, meal_plan: meal_plan, package_id: meal_plan.uuid, name: 'team order3', delivery_at: delivery_start + 3.days) }

  it 'lists all package orders for the passed in meal_plan' do
    package_orders = MealPlans::ListPackageOrders.new(meal_plan: meal_plan).call

    expect(package_orders).to include(team_order1, team_order2, team_order3)
  end

  it 'return empty if passed in meal plan is not an individual meal plan' do
    meal_plan.update_column(:kind, 'shared')
    package_orders = MealPlans::ListPackageOrders.new(meal_plan: meal_plan).call

    expect(package_orders).to be_empty
  end

  it 'filters active team orders' do
    team_order2.update_column(:status, %w[delivered rejected cancelled skipped].sample)
    filter_options = { active_only: true }
    package_orders = MealPlans::ListPackageOrders.new(meal_plan: meal_plan, options: filter_options).call

    expect(package_orders).to_not include(team_order2)
    expect(package_orders).to include(team_order1, team_order3)
  end

  it 'sorts the orders by delivery datetime (default)' do
    package_orders = MealPlans::ListPackageOrders.new(meal_plan: meal_plan).call

    expect(package_orders).to eq([team_order2, team_order1, team_order3])
  end

  it 'sorts the orders by the passed in config' do
    filter_options = { order_by: 'name' } # as a field name
    package_orders = MealPlans::ListPackageOrders.new(meal_plan: meal_plan, options: filter_options).call
    expect(package_orders).to eq([team_order1, team_order2, team_order3])

    filter_options = { order_by: { delivery_at: :desc } } # as a hash with direction
    package_orders = MealPlans::ListPackageOrders.new(meal_plan: meal_plan, options: filter_options).call
    expect(package_orders).to eq([team_order3, team_order1, team_order2])
  end

  context 'delivery scoped orders' do
    let!(:team_order21) { create(:order, :package_team_order, meal_plan: meal_plan, package_id: meal_plan.uuid, name: 'team order21', delivery_at: delivery_start + 1.week + 2.days) }
    let!(:team_order22) { create(:order, :package_team_order, meal_plan: meal_plan, package_id: meal_plan.uuid, name: 'team order22', delivery_at: delivery_start + 2.weeks + 1.days) }
    let!(:team_order23) { create(:order, :package_team_order, meal_plan: meal_plan, package_id: meal_plan.uuid, name: 'team order23', delivery_at: delivery_start + 1.month + 3.days) }

    it 'lists all orders by default' do
      filter_options = { scoped_to: ['all', 'any_other_scope', nil].sample }
      package_orders = MealPlans::ListPackageOrders.new(meal_plan: meal_plan, options: filter_options).call

      expect(package_orders).to include(team_order1, team_order2, team_order3, team_order21, team_order22, team_order23)
    end

    it 'filters future team orders' do
      team_order3.update_column(:delivery_at, delivery_start - 1.day)
      filter_options = { scoped_to: 'future_only', scoped_time: delivery_start }
      package_orders = MealPlans::ListPackageOrders.new(meal_plan: meal_plan, options: filter_options).call

      expect(package_orders).to_not include(team_order3)
      expect(package_orders).to include(team_order1, team_order2, team_order21, team_order22, team_order23)
    end

    it 'lists orders within the recent month of the passed in scoped time' do
      filter_options = { scoped_to: 'recent_month', scoped_time: delivery_start }
      package_orders = MealPlans::ListPackageOrders.new(meal_plan: meal_plan, options: filter_options).call

      expect(package_orders).to include(team_order1, team_order2, team_order3, team_order21, team_order22)
      expect(package_orders).to_not include(team_order23)
    end

    it 'lists orders within the recent fortnight of the passed in scoped time' do
      filter_options = { scoped_to: 'recent_fortnight', scoped_time: delivery_start }
      package_orders = MealPlans::ListPackageOrders.new(meal_plan: meal_plan, options: filter_options).call

      expect(package_orders).to include(team_order1, team_order2, team_order3, team_order21)
      expect(package_orders).to_not include(team_order22, team_order23)
    end

    it 'lists orders within the recent week of the passed in scoped time' do
      filter_options = { scoped_to: 'recent_week', scoped_time: delivery_start }
      package_orders = MealPlans::ListPackageOrders.new(meal_plan: meal_plan, options: filter_options).call

      expect(package_orders).to include(team_order1, team_order2, team_order3)
      expect(package_orders).to_not include(team_order21, team_order22, team_order23)
    end
  end

end
