require 'rails_helper'

RSpec.describe MealPlans::RegisterAttendee, type: :service, meal_plans: true, attendees: true do
  
  let!(:customer) { create(:customer_profile, :random, :with_user) }  
  let!(:meal_plan) { create(:meal_plan, :random, :team_meal_plan, customer_profile: customer) }

  let!(:attendee_params) do
    {
      first_name: Faker::Name.first_name,
      last_name: Faker::Name.last_name,
      email: Faker::Internet.email
    }
  end

  before do
    email_sender = delayed_email_sender = double(MealPlans::Emails::SendAttendeeRegistrationEmail)
    allow(MealPlans::Emails::SendAttendeeRegistrationEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:delay).and_return(delayed_email_sender)
    allow(delayed_email_sender).to receive(:call).and_return(true)
  end

  it 'create a new event attendee for the meal plan customer' do
    expect(customer.event_attendees).to be_blank

    attendee_registration = MealPlans::RegisterAttendee.new(meal_plan: meal_plan, attendee_params: attendee_params).call
    expect(attendee_registration).to be_success

    registered_event_attendee = attendee_registration.event_attendee
    expect(registered_event_attendee).to be_present
    expect(registered_event_attendee).to be_persisted
    expect(registered_event_attendee).to be_a(EventAttendee)
    expect(registered_event_attendee.team_admin).to eq(customer) # meal_plan.customer_profile

    expect(registered_event_attendee.first_name).to eq(attendee_params[:first_name])
    expect(registered_event_attendee.last_name).to eq(attendee_params[:last_name])
    expect(registered_event_attendee.email).to eq(attendee_params[:email])    
    expect(customer.event_attendees).to be_present
  end

  it 'create a meal plan attendee and attaches it the the meal plan' do
    attendee_registration = MealPlans::RegisterAttendee.new(meal_plan: meal_plan, attendee_params: attendee_params).call
    expect(attendee_registration).to be_success

    registered_meal_plan_attendee = attendee_registration.meal_plan_attendee
    expect(registered_meal_plan_attendee).to be_present
    expect(registered_meal_plan_attendee).to be_persisted
    expect(registered_meal_plan_attendee).to be_a(MealPlanAttendee)
    expect(registered_meal_plan_attendee.meal_plan).to eq(meal_plan)
    expect(registered_meal_plan_attendee.status).to eq('registered')
    expect(registered_meal_plan_attendee.uuid).to be_present

    expect(registered_meal_plan_attendee.first_name).to eq(attendee_params[:first_name])
    expect(registered_meal_plan_attendee.last_name).to eq(attendee_params[:last_name])
    expect(registered_meal_plan_attendee.email).to eq(attendee_params[:email])    
  end

  it 'sends an email to notify the attendee about the registration' do
    expect(MealPlans::Emails::SendAttendeeRegistrationEmail).to receive(:new).with(meal_plan_attendee: anything, meal_plan: meal_plan) # created meal plan attendee

    attendee_registration = MealPlans::RegisterAttendee.new(meal_plan: meal_plan, attendee_params: attendee_params).call
    expect(attendee_registration).to be_success    
  end

  context 'with an existing event attendee' do
    let!(:event_attendee) { create(:event_attendee, :random, team_admin: customer, email: attendee_params[:email]) }

    it 'returns the existing event attendee' do
      attendee_registration = MealPlans::RegisterAttendee.new(meal_plan: meal_plan, attendee_params: attendee_params).call
      expect(attendee_registration).to be_success

      registered_event_attendee = attendee_registration.event_attendee
      expect(registered_event_attendee).to be_present
      expect(registered_event_attendee.id).to eq(event_attendee.id) # same as the existing event attendee      
    end

    context 'with an existing meal plan attendee' do
      let!(:meal_plan_attendee) { create(:meal_plan_attendee, :random, meal_plan: meal_plan, event_attendee: event_attendee) }

      it 'returns the existing meal plan attendee with a warning' do
        attendee_registration = MealPlans::RegisterAttendee.new(meal_plan: meal_plan, attendee_params: attendee_params).call

        expect(attendee_registration).to_not be_success
        expect(attendee_registration.warnings).to include('You are already registered as an attendee for this event, please check your email for an order invite link or contact your team admin')

        registered_meal_plan_attendee = attendee_registration.meal_plan_attendee
        expect(registered_meal_plan_attendee).to be_present
        expect(registered_meal_plan_attendee.id).to eq(meal_plan_attendee.id) # same as the existing meal plan attendee      
      end

      it 'sends an email to notify the existing meal plan attendee about their existing registration' do
        expect(MealPlans::Emails::SendAttendeeRegistrationEmail).to receive(:new).with(meal_plan_attendee: meal_plan_attendee, meal_plan: meal_plan)

        attendee_registration = MealPlans::RegisterAttendee.new(meal_plan: meal_plan, attendee_params: attendee_params).call
        expect(attendee_registration).to_not be_success
      end
    end # existing meal plan attendee
  end # existing event attendee


  context 'errors' do
    it 'returns with errors if the meal plan is missing' do
      attendee_registration = MealPlans::RegisterAttendee.new(meal_plan: nil, attendee_params: attendee_params).call

      expect(attendee_registration).to_not be_success
      expect(attendee_registration.errors).to include('This event invite link has expired')
    end

    it 'returns with errors when registering with the Meal Plan\'s customer' do
      admin_attendee_params = attendee_params.merge({ email: customer.email })

      attendee_registration = MealPlans::RegisterAttendee.new(meal_plan: meal_plan, attendee_params: admin_attendee_params).call

      expect(attendee_registration).to_not be_success
      expect(attendee_registration.warnings).to include('You are already registered as an attendee for this event, please check your email for an order invite link')
    end

    it 'returns with errors with missing event attendee params' do
      attendee_registration = MealPlans::RegisterAttendee.new(meal_plan: meal_plan, attendee_params: {}).call

      expect(attendee_registration).to_not be_success
      expect(attendee_registration.errors).to include('First name can\'t be blank', 'Email can\'t be blank', 'Email is invalid')
    end
  end # errors

end