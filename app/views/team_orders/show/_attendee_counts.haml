:ruby
  show_package_counts ||= false
  lister_options = {
    order: team_order,
    include_package_attendees: show_package_counts
  }
  all_attendees = TeamOrderAttendees::List.new(options: lister_options).call
  
  if show_package_counts   
   team_order_attendees, package_attendees = all_attendees.partition{|attendee| attendee.present? && attendee.is_a?(TeamOrderAttendee) && attendee.order_id == team_order.id }
   pending_attendees = team_order_attendees.select{|attendee| %w[invited pending].include?(attendee.status) }
   pending_attendees += package_attendees
  else
    team_order_attendees = all_attendees
    pending_attendees = team_order_attendees.where(status: %w[invited pending])
  end

  ordered_attendees = team_order_attendees.select{|attendee| attendee.status == 'ordered' }
  declined_attendees = team_order_attendees.select{|attendee| attendee.status == 'declined' }

%p.attendee-count= all_attendees.select{|attendee| attendee.status != 'cancelled' }.size
%p.team-order-attendees-status.ordered
  %span= "#{ordered_attendees.size} Ordered"
%p.team-order-attendees-status.invited
  %span= "#{pending_attendees.size} Pending"
%p.team-order-attendees-status.declined
  %span= "#{declined_attendees.size} Declined"
