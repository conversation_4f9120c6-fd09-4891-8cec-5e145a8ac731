- content_for :back_button do
  = link_to customer_team_orders_path(uuid: @package_order.meal_plan.uuid, date: @package_order.delivery_at.to_s(:date_spreadsheet)) do
    %span.customer-header-back-link

- content_for :header_title, 'Team Order Package'

:ruby
  last_package_order = TeamOrders::ListPackageOrders.new(team_order: @package_order).call.last
  if @package_order.is_recurring_team_order?
    extension_type = 'weekly'
    extension_week = (last_package_order.delivery_at + 1.week).beginning_of_week(:sunday)
  else
    extension_type = 'add-more'
  end

.team-order-package-page.team-order-package-page--admin{ data: { view_team_order_show: true, view_team_order_load_more: true } }
  .show-package.show-package--admin
    .show-package__container
      .show-package__banner
        %p= @package_order.name
        .float-right
          .between-flex
            %p.show-package__magic-link Magic Link:
            %p.team-order-details__info.attendee-invite-link
              = new_team_order_package_attendee_invite_url(package_id: @package_order.package_id)

      = render 'team_orders/package_orders_list'

    .row.small-12.columns.between-flex.pt-2
      %a.button{ href: extend_team_order_path(last_package_order) }
        - if extension_type == 'weekly'
          Select suppliers for week starting #{extension_week.to_s(:date_verbose)}
        - else
          Extend / Add to package
