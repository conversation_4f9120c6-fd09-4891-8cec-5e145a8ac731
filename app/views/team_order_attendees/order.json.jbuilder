if (warnings = @supplier_order.warnings.presence)
  json.warnings warnings.map{|warning| I18n.t("team_order.warnings.#{warning}") }
end
supplier = @supplier_menu.supplier
team_order = @supplier_order.order
json.partial! 'suppliers/team_order_supplier', supplier: supplier

attendee = @supplier_order.team_order_attendee.presence
logged_in_as_team_admin = attendee&.is_team_admin? || (session_profile.present? && team_order.customer_profile == session_profile)

if attendee.present?
  json.attendee do
    json.extract! attendee, :id, :name, :team_order_level_id
    json.uuid attendee.uniq_code
    if team_order.is_package_order?
      attendee_package_code = attendee.meal_plan_attendee&.uuid || attendee.uniq_code
      json.package_url team_order_attendee_package_url(code: attendee_package_code, scoped_time: team_order.delivery_at.to_s(:date_spreadsheet)) 
    end
    json.is_team_admin logged_in_as_team_admin
  end
end

json.order do
  json.extract! team_order, :id, :team_order_budget, :name
  json.delivery_at team_order.delivery_at.to_s(:full_verbose)
  if (attendee_levels = team_order.attendee_levels.presence)
    json.attendee_levels attendee_levels.each do |attendee_level|
      json.extract! attendee_level, :id, :name
    end
  end
  orders_to_render = [team_order]
  json.set! :orders do
    orders_to_render.map do |order_to_render|
      json.set! order_to_render.id do
        json.partial! 'api/orders/checkout_order', order: order_to_render, attendee: @supplier_order.team_order_attendee
      end
    end
  end

  json.order_suppliers do
    team_order.team_supplier_profiles.map do |supplier|
      json.set! supplier.id do
        json.extract! supplier, :id, :name
        json.image_id  supplier.profile.avatar
      end
    end
  end
end

json.section_grouped_menu_items @supplier_menu.section_grouped_menu_items.each do |menu_section, section_menu_items|
  json.partial! 'menu_sections/menu_section', menu_section: menu_section, menu_items: section_menu_items, grouped_rate_cards: @supplier_menu.grouped_rate_cards, hide_over_budget: !logged_in_as_team_admin
end
