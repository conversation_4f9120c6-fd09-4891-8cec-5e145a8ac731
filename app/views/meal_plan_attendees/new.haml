:ruby
  team_admin = @meal_plan&.customer_profile

- content_for :webpack, 'team_order'

.customer-area.has-large-gutter
  .auth-container
    .auth-card
      .auth-card__illustration
        - if @meal_plan.blank?
          = image_tag 'illustrations/search.svg'
          %h4.auth-card__title Oops! Too Late!
        - else
          = image_tag 'illustrations/impress.svg'
          %h4.auth-card__title Ready to order?
          %p
            Yum! You've been invited to
            %strong
              #{@meal_plan.name} Package.
            Enter your details to register and we'll send you an email with further details.
      .authorization-module
        - if @meal_plan.blank?
          .login-heading
            Whoops!
          %p{ style: 'margin-top: 2rem' }
            Looks like this event has expired. Please contact your team admin for further information.
        - else
          = form_for EventAttendee.new, method: 'POST', url: meal_plan_attendees_path(uuid: @meal_plan.uuid), html: { class: 'authorization-form', data: { abide: '' }, novalidate: '' } do |f|
            = f.hidden_field :team_admin_id, value: team_admin.id
            %h3.login-heading
              Enter Your Details
              %span.whats-this{ class: 'bottom tooltip-dash', tabindex: '2', title: 'If you\'ve already registered, you can re-enter the same email address and we\'ll re-send the invite email.', data: { tooltip: true, 'tooltip-class': 'whats-this__tooltip' } }
                %span.hide-for-small-only
                  Already
                Registered?
            .row
              .small-12.medium-6.columns
                %label.input-label First Name
                = f.text_field :first_name, placeholder: 'Jane', class: 'validate input', required: true
                %span.form-error Please enter your first name
              .small-12.medium-6.columns
                %label.input-label Last Name
                = f.text_field :last_name, placeholder: 'Doe', class: 'validate input', required: true
                %span.form-error Please enter your last name
            .row
              .small-12.columns
                %label.input-label{style: 'display: inline-block'} Email
                %span.whats-this{ class: 'bottom tooltip-dash', tabindex: '2', title: "Your email will only be used for the purpose of signing-up to this package. We will never use it for anything else.", data: { tooltip: true, 'tooltip-class': 'whats-this__tooltip' }, style: 'font-size: 0.875rem; margin-left: 12px;' } Privacy
                = f.text_field :email, placeholder: '<EMAIL>', class: 'validate input', required: true, pattern: 'email'
                %span.form-error Please enter a valid email

            - if false && @meal_plan.attendee_levels.present?
              .row
                .small-12.columns
                  %label.input-label{style: 'display: inline-block'}
                    Level
                    %small (where we'll deliver the food)
                  = select_tag 'event_attendee[level_id]', options_from_collection_for_select(@meal_plan.attendee_levels, :id, :name), { prompt: 'Select a level', class: 'validate input', required: true }
                  %span.form-error Please select a level
            .row
              .small-12.columns
                %button.button.button-large.submit-new-attendee{ type: 'submit' }
                  Add me to #{@meal_plan.name}! Package
