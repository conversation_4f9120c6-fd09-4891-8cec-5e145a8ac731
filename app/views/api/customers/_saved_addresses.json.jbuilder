cache_key = [customer.cache_key, 'saved-addresses', suburb&.cache_key]

customer_recent_addresses = Rails.cache.fetch(cache_key, expires_in: 12.hours) do
  Customers::ListOrderAddresses.new(customer: customer, suburb: suburb).call
end

customer_recent_addresses ||= []

json.recent_addresses customer_recent_addresses.map do |address|
  address_suburb = address.suburb
  json.id SecureRandom.hex(5)
  json.extract! address, :level, :street_address, :label, :instructions
  json.suburb_label address_suburb.label
  json.suburb_id address_suburb.id
  json.postcode address_suburb.postcode
end

json.saved_addresses customer.saved_addresses.map do |address|
  address_suburb = address.suburb
  json.extract! address, :id, :level, :street_address, :instructions
  json.label [address.level, address.street_address].reject(&:blank?).join(', ')
  json.suburb_label address_suburb.label
  json.suburb address_suburb.name
  json.suburb_id address_suburb.id
  json.postcode address_suburb.postcode
end
