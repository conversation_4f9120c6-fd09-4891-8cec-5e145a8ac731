selected_supplier_profiles = @selected_order_suppliers.map(&:supplier_profile)
json.suppliers @suppliers.each do |supplier|
  json.extract! supplier, :id, :name, :rating, :slug
  json.company_name supplier.company_name
  json.is_new supplier.is_new?
  json.selected selected_supplier_profiles.include?(supplier)
  json.is_favourite @favourite_team_supplier_ids.include?(supplier.id)
  json.has_custom_pricing (@rate_card_supplier_ids.include?(supplier.id) || @markup_override_supplier_ids.include?(supplier.id))
  json.has_custom_menu @custom_menu_supplier_ids.include?(supplier.id)

  # Supplier minimums data
  supplier_minimums = @suppliers_minimums[supplier]
  if supplier_minimums.present?
    json.minimum_spend supplier_minimums.minimum_spend
    json.lead_time supplier_minimums.lead_time
  else
    json.minimum_spend 0
    json.lead_time '-'
  end

  # Image data
  if supplier.profile&.avatar&.public_id.present?
    json.image_id supplier.profile.avatar.public_id
  else
    json.image_id nil
  end

  # Best team size calculation
  if params[:budget].present? && supplier_minimums.present?
    best_for = supplier_minimums.minimum_spend / params[:budget].to_i
    json.best_team_size best_for.to_i
  else
    json.best_team_size 1
  end
end

if params[:wants_html]
  json.suppliers_html (render partial: 'team_orders/suppliers_list', locals: { suppliers: @suppliers, suppliers_minimums: @suppliers_minimums, budget: (params[:budget].presence || nil), selected_order_suppliers: @selected_order_suppliers, favourite_team_supplier_ids: @favourite_team_supplier_ids })
end
