json.orders_size @package_orders.size

json.attendee do
  json.name @meal_plan_attendee.first_name.try(:capitalize)
end

json.order_week_scope @package_orders&.first&.delivery_at&.beginning_of_week(:monday)&.strftime("%Y-%m-%d")

json.orders @package_orders.each do |team_order|
  json.extract! team_order, :id, :status
  json.delivery_at team_order.delivery_at.strftime("%A %dth %B - %_I:%M%P")

  team_order_cutoff = TeamOrders::FetchCutoffDayHour.new(team_order: team_order).call
  json.cutoff do
    json.extract! team_order_cutoff, :is_today?, :hours_left, :days_left
    json.has_expired team_order_cutoff.has_expired?
  end

  # supplier related data
  json.suppliers team_order.team_supplier_profiles.each do |supplier|
    json.extract! supplier, :id, :name
    json.image supplier.profile&.avatar.present? ? cl_image_path(supplier.profile.avatar, width: 400, height: 240, crop: 'fill') : nil
  end  

  # attendee related data
  team_order_attendee = team_order.team_order_attendees.where(event_attendee: @meal_plan_attendee.event_attendee).first
  if team_order_attendee.blank?
    team_order_attendee = team_order.team_order_attendees.where(event_attendee: @meal_plan_attendee.event_attendee, uniq_code: @meal_plan_attendee.uuid).first_or_initialize
  end
  attendee_order_lines = team_order_attendee.persisted? ? team_order.order_lines.where(team_order_attendee: team_order_attendee) : []

  case
  when team_order_cutoff.has_expired?
    unsubscribe_url = order_url = nil
  when team_order.is_package_order? && !team_order_attendee.persisted?
    unsubscribe_url = team_order_attendee_unsubscribe_registered_package_order_post_url(code: team_order_attendee.uniq_code, event_id: team_order.unique_event_id, format: :json)
    order_url = team_order_attendee_registered_package_order_url(code: team_order_attendee.uniq_code, event_id: team_order.unique_event_id)
  else
    unsubscribe_url = team_order_attendee_unsubscribe_url(code: team_order_attendee.uniq_code, format: :json)
    order_url = next_app_team_order_attendee_order_url(code: team_order_attendee.uniq_code)
  end

  json.expiry_options team_order_expiry_options(
    team_order_cutoff: team_order_cutoff,
    attendee_confirmed_order: team_order_attendee.status == 'ordered'
  )
  json.team_order_attendee do
    json.extract! team_order_attendee, :status, :uniq_code
    json.order_lines attendee_order_lines.each do |order_line|
      json.extract! order_line, :quantity, :name
    end
    json.order_url order_url
    json.unsubscribe_url unsubscribe_url
  end
end
