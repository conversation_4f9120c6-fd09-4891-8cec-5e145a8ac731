order = notification.loggable
customer = notification.scopable
info = notification.info

suppliers = order.is_team_order? ? order.team_supplier_profiles : order.supplier_profiles
supplier_count = suppliers.count
supplier_info = supplier_count > 1 ? "#{supplier_count} Suppliers" : suppliers.first&.company_name

message = case notification.event
when 'new-order-quoted'
  "<b>#{customer.name}</b> was quoted an order with <b>#{supplier_info} for #{order.delivery_at.to_s(:full_verbose)}"
when 'new-order-submitted'
  "<b>#{customer.name}</b> placed a new order with <b>#{supplier_info}</b> for #{order.delivery_at.to_s(:full_verbose)}"
when 'new-amazon-order'
  "<b>#{customer.name}</b> placed a <b>new AMAZON order</b> for #{order.delivery_at.to_s(:full_verbose)}. <b>Urgently!</b> place this order on Amazon website directly."
when 'woolworths-checkout-failed'
  "A <b>Woolworths order</b> ##{order.id} for <b>#{customer.name}</b>, has <b>failed to process with Woolworths</b>, in account <b>#{info['account']}</b>"
when 'order-rejected'
  "<b>#{info['supplier']}</b> rejected an order ##{order.id} for <b>#{customer.name}</b>"
when 'order-canceled'
  "<b>#{customer.name}</b> has had their order <b>cancelled</b> for #{order.delivery_at.to_s(:full_verbose)}"
when 'order-canceled-permanently'
  "<b>#{customer.name}</b> has had their order <b>permanently cancelled</b> for #{order.delivery_at.to_s(:full_verbose)}"
when 'on-hold-charge-failed'
  "The <b>On-Hold card charge FAILED</b> for order ##{order.id} by #{customer.name} with message: #{info['message']}"
# team order related
when 'new-team-order-created'
  "<b>#{customer.name}</b> placed a new team order with <b>#{supplier_info}</b> for #{order.delivery_at.to_s(:full_verbose)}"
when 'new-package-created'
  package_link = sign_in_as_customer_path(customer.user, redirect_path: customer_team_orders_path(uuid: order.meal_plan.uuid, date: order.delivery_at.to_s(:date_spreadsheet)))
  "<b>#{customer.name}</b> placed a new team order package named <a href='#{package_link}'><b>#{order.name}</b></a>"
when 'package-extended'
  package_link = sign_in_as_customer_path(customer.user, redirect_path: customer_team_orders_path(uuid: order.meal_plan.uuid, date: order.delivery_at.to_s(:date_spreadsheet)))
  "<b>#{customer.name}</b> extended a new team order package named <a href='#{package_link}'><b>#{order.name}</b></a>"
when 'approaching-cutoff'
  "<b>#{customer.name}</b> has a team order <b>approaching cutoff in #{TeamOrders::Emails::SendAdminCutOffEmail::REMAINING_TIME_LABEL[info['cutoff']]}</b>"
when 'approaching-cutoff-below-minimum'
  "<b>#{customer.name}</b> has a team order approaching cutoff in #{TeamOrders::Emails::SendAdminCutOffEmail::REMAINING_TIME_LABEL[info['cutoff']]} <b>below supplier minimums by $#{info['remaining_spend']}</b>"
# custom order related
when 'custom-order-saved-as-draft'
  "<b>#{customer.name}</b> has a custom order with <b>#{supplier_info}</b> saved as a draft"
when 'new-custom-order-quoted'
  "<b>#{customer.name}</b> was quoted a custom order with <b>#{supplier_info} for #{order.delivery_at.to_s(:full_verbose)}</b>"
when 'new-custom-order-submitted'
  "A new custom order ##{order.id} for <b>#{customer.name}</b> has been placed with <b>#{supplier_info}</b> for #{order.delivery_at.to_s(:full_verbose)}"
when 'order-below-margin-threshold'
  "<b>#{customer.name}</b> has a custom order ##{order.id} that's <b>below the margin threshold: #{info['commission']}%</b>"
end

if %w[new-order-submitted order-amended].include?(notification.event)
  order_status = notification.event == 'new-order-submitted' ? 'placed a new' : 'amended an'
  if info['after_cutoff'].present?
    json.submitted_after_cutoff true
    message = "<b>#{customer.name}</b> #{order_status} order <b>after supplier cutoff</b>"
  end

  if info['under_supplier_minimum'].present?
    supplier_spends = info['supplier_spends']
    json.under_supplier_minimum true
    json.supplier_spends supplier_spends
    message = "<b>#{customer.name}</b> #{order_status} order <b>below supplier minimums by $#{supplier_spends.first['remaining']}</b>"
  end
end

json.message message
