order_links = []

cancel_links = []

# view links
view_or_manage = team_order.status == 'pending' ? 'Manage This Order' : 'View Order'
order_links << { type: 'view-package', url: customer_team_orders_path(uuid: team_order.meal_plan.uuid, date: team_order.delivery_at.to_s(:date_spreadsheet)), label: 'Show All Linked Orders'} if team_order.is_package_order?
order_links << { type: 'view', url: team_order_path(team_order), label: view_or_manage }

order_links << { type: 'edit', url: edit_team_order_path(team_order), label: 'Edit Order' } if team_order.status == 'pending'

can_cancel_team_order = is_admin? ? %w[cancelled delivered].exclude?(team_order.status) : team_order.status == 'pending'
if can_cancel_team_order
  cancel_links << { type: 'cancel-one-off', url: api_order_path(team_order, format: :json), label: 'Cancel This Order' }
  cancel_links << { type: 'cancel-and-notify', url: api_order_path(team_order, notify_attendees: true, format: :json), label: 'Cancel Order and Notify Attendees' }
end

if cancel_links.size == 1
  order_links += cancel_links
  cancel_links = []
elsif cancel_links.present?
  order_links << { type: 'cancel-options', url: nil, label: 'Cancel Order' }
end

json.links order_links
json.cancel_links cancel_links
