:ruby
  is_attendee ||= false
  show_budget = meal_plan.budget.present? && ( (is_attendee && !meal_plan.hide_budget) || !is_attendee )
  meal_plan_orders ||= []

%table{ width: '100%' }
  %tbody
    %tr
      %td{ colspan: 2 }
        %strong Event Details:
    %tr
      %td{ style: 'width: 40%;' }
        %strong Name:
      %td{ style: 'width: 60%;' }
        = meal_plan.name

    - if meal_plan.number_of_attendees
      %tr
        %td{ style: 'width: 40%;' }
          %strong # of invited attendees:
        %td{ style: 'width: 60%;' }
          = meal_plan.number_of_attendees

    - if show_budget
      %tr
        %td{ style: 'width: 40%;' }
          %strong Budget:
        %td{ style: 'width: 60%;' }
          = "#{meal_plan.budget} per head"
          - if meal_plan.hide_budget
            (hidden from attendees)

    %tr
      %td{ style: 'width: 40%; vertical-align: top' }
        %strong
          Address:
      %td{ style: 'width: 60%;' }
        =raw meal_plan.delivery_address

- if meal_plan_orders.present?
  = render 'layouts/emails/spacer', height: 20, color: '#ffffff'

  %table{ width: '100%' }
    %thead
      %tr{style: 'text-align: left;'}
        %th{ width: '50%' }
          Supplier
        %th{ width: '50%' }
          Date
    %tbody
      %tr
        %td{ style: 'height: 2px; border-top: 1px solid #ededed;', colspan: 2 }

      - meal_plan_orders.each do |order|
        %tr
          %td{width: '50%'}
            %table
              %tbody
                %tr
                  %td
                    = render 'emails/circle_image', image: order.supplier.image, name: order.supplier.name
                  %td
                    = order.supplier.name

          %td{ width: '50%' }
            %strong
              = order.delivery_at

        - if order.minimum_spend.present? && order.cutoff_datetime.present?
          %tr
            %td{ colspan: 2, style: 'text-align: left; border-bottom: 1px solid #ededed;' }
              %em{ style: 'font-size: 12px' }
                Order has a minimum spend of
                %strong
                  = order.minimum_spend
                by #{order.cutoff_datetime}.

      %tr
        %td{ style: 'height: 2px; border-top: 1px solid #ededed;', colspan: 2 }
