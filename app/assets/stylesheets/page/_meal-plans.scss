.meal-plan-app {
  @include media-down(hamburger) {
    display: none;
  }
}

.meal-plan-week-dates {
  width: 130px;
  border: 1px solid #eaeaea;
}

.mobile-warning {
  display: none;
  @include media-down(hamburger) {
    display: block;
  }
}

.meal-plan-container {
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.2);
  .po-select {
    font-size: 14px;
  }
}

.change-container {
  display: flex;
  > * {
    flex: 1;
  }
}

.meal-plan-toolbar {
  display: flex;
  > div {
    border: 1px solid #eaeaea;
    border-bottom: none;
    padding: 6px;
    width: 130px;
    text-align: center;
    cursor: pointer;
    &:first-of-type {
      border-top-left-radius: 6px;
    }
    &:last-of-type {
      border-left: none;
      border-top-right-radius: 6px;
    }
    &.active {
      background: #000;
      color: white;
      border: none;
    }
  }
}

.meal-plan-days {
  display: grid;
  grid-template-columns: 130px 1fr 1fr 1fr 1fr 1fr;
  align-items: center;
  > span, div {
    min-width: 130px;
    text-align: center;
  }
  > span {
    font-weight: bold;
  }
}
.meal-plan-week {
  display: grid;
  grid-template-columns: 130px 1fr 1fr 1fr 1fr 1fr;
  &-dates {
    font-weight: bold;
    padding-top: 12px;
    text-align: center;
  }
  &.striped {
    background: #f5f5f5;
    .meal-event {
      border-bottom: 1px solid #eaeaea;
    }
  }
  > div {
    min-width: 130px;
    min-height: 140px;
  }
}

.meal-plan-day {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border: 1px solid #eaeaea;
  border-left: none;
  position: relative;
  & + .meal-plan-day {
    border-left: none;
  }
  &.today {
    border: 2px solid $primary;
    background: #f0f9f0;
  }
  .day-number {
    position: absolute;
    top: 0;
    left: 0;
    font-size: 12px;
    font-weight:bold;
    margin-left: 4px;
    color:black;
    display:inline-block;
    &.spacer {
      position: static;
      color: grey;
    }
    &.today {
      text-align: center;
      width: 20px;
      height: 20px;
      line-height: 20px;
      background: $primary;
      border-radius: 50%;
      color:white;
      font-weight: bold;
    }
  }
}

.meal-plan-change-week {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  background-color: $primary;
  border-radius: 0;
  cursor: pointer;
  &:hover {
    background: darken($primary, 10%);
  }
  &:first-of-type {
    border-right: 1px solid white;
  }
  &::after {
    content: '';
    display: inline-block;
    background-image: url("icons/chevron-down.svg");
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
    width: 20px;
    height: 20px;
    filter: invert(1);
    line-height: 20px;
  }
  &.back::after {
    transform: rotate(180deg);
  }
}

.meal-plans {
  display: flex;
  margin-bottom: 30px;
}

.meal-plan {
  padding: 4px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  margin-right: 20px;
  cursor: pointer;
  &.selected {
  background: black;
  color: white;
  .meal-plan-staff-count::before {
    filter: invert(1);
  }
  }
}

.meal-plan-name {
  margin-bottom: 4px;
}

.meal-plan-staff-count {
  display: flex;
  align-items: center;
  margin-bottom: 0;
  min-width: 160px;
  &::before {
    content: '';
    display: inline-block;
    background-image: url("icons/staff.svg");
    background-repeat: no-repeat;
    background-size: contain;
    width: 20px;
    height: 20px;
    margin-right: 12px;
  }
}

.add-meal-plan {
  display: flex;
  align-items: center;
  background: #E8E8E8;
  padding: 4px 12px;
  border-radius: 4px;
  cursor: pointer;
  &:hover {
    background: darken(#e8e8e8, 10%)
  }
  &::before {
    content: '';
    display: inline-block;
    background: url("icons/plus-hollow.svg");
    background-repeat: no-repeat;
    background-size: contain;
    width: 20px;
    height: 20px;
    margin-right: 12px;
  }
}

.add-meal-plan-day {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e2e2e2;
  border-left: none;
  border-right: none;
  color: black;
  transition: all .3s ease-out;
  &:hover {
    background: black;
    color: white;
    &::before {
      filter: invert(1);
    }
  }
  &::before {
    content: '';
    display: inline-block;
    background: url("icons/plus-hollow.svg");
    background-repeat: no-repeat;
    background-size: contain;
    width: 16px;
    height: 16px;
    margin-right: 12px;
  }
  &.close::before {
    background: url("icons/minus.svg");
  }
}

.meal-event {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-top: 1px solid #d9d9d9;
  border-bottom: 1px solid #d9d9d9;
  &.link {
    position: relative;
    color: black;
    font-size: 16px;
    transition: background .1s ease-in;
    cursor: pointer;
    &:hover {
      background: #dedede;
    }
  }
  .calendar-view-slider {
    content: ' ';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }
  &--holiday {
    background-color: #ffe6e6;
    padding: 0;
    justify-content: center;
    font-size: 12px;
    cursor: pointer;
    > div {   
      max-width: 150px;
      text-wrap: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .event-image {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    margin-right: 8px;
  }
  .event-info {
    background: black;
    color: white;
    margin-left: auto;
    padding: 4px 8px;
    border-radius: 4px;
    &.ellipsis {
      display: inline-block;
      background-color: initial;
      background-image: url(icons/ellipsis-primary.svg);
      background-repeat: no-repeat;
      width: 22px;
      height: 22px;
      &:hover {
        background-color: initial;
      }
    }
    &:hover {
      background-color: #5c5c5c;
    }
  }
  @include media-down(desktop) {
    .event-image {
      width: 30px;
      height: 30px;
    }
    .event-time {
      font-size: 14px;
    }
    .event-info {
      padding: 2px 4px;
      font-size: 12px;
    }
  }
}

.event-type {
  &::before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    margin-right: 8px;
  }
  &.new::before, &.confirmed::before, &.delivered::before, &.amended::before, &.pending::before {
    background-image: url("icons/check-circle.svg")
  }
  &.quoted::before, &.paused::before {
    background-image: url("icons/pending.svg");
  }
  &.quoted::before {
    filter: grayscale(1);
  }
  &.cancelled::before, &.skipped::before {
    background-image: url("icons/cancel-block.svg")
  }
}

.event-time {
  @include media-down(tablet) {
    display: none;
  }
}

.meal-plan-list {
  display: flex;
  gap: 1rem;
}

.meal-plan-card {
  border: 1px solid #ccc;
  padding: 1rem;
  width: 200px;
  &.selected {
    border: 2px solid $primary;
  }
  &__name {
    font-size: 20px;
    font-weight: bold;
  }
  &__nop {
    display: block;
  }
}

.form-section {
  .section-heading {
    font-size: 20px;
    margin-top: 1rem;
  }
}

.meal-plan-address {
  .saved-address-list {
    overflow: scroll;
    max-height: 400px;
  }

  input.location-search-input {
    width: 100%;
    border-radius: 4px;
    box-shadow: none!important;
    padding: 16px 10px;
    background-repeat: no-repeat;
    &.icon {
      background-image: url("icons/marker.svg");
      background-size: 14px;
      background-position: 2% 50%;
      background-color: #f5f5f5;
      padding: 16px 40px;
    }
    @include media-down(tablet) {
      box-shadow: none;
    }
  }

  .address-back::before {
    content: '';
    display: inline-block;
    width: 24px;
    height: 24px;
    background: url('icons/chevron-back.svg') no-repeat;
  }
  .saved-address {
    display: grid;
    grid-template-columns: 50px auto 50px;
    border: 1px solid rgb(202, 202, 202);
    padding: 6px 10px;
    margin: 10px 0;
    border-radius: 4px;
    line-height:  20px;
    cursor: pointer;
    &:hover {
      background: #f5f5f5;
      outline: none;
    }
    &::before {
      content: '';
      display: inline-block;
      background: url("icons/marker.svg");
      background-size: contain;
      width: 20px;
      height: 20px;
      margin-right: 10px;
      background-repeat: no-repeat;
      align-self: center;
    }
    p {
      margin-bottom: 0;
      font-size: 0.8rem;
    }
    p.bold {
      font-weight: bold;
    }
    p.grey {
      color: #717171;
    }

    &__edit {
      align-self: center;
      &::before {
        content: '';
        display: inline-block;
        width: 24px;
        height: 24px;
        background: url("icons/pencil.svg")
      }
    }
  }
}

.meal-plan-billing {
  .invoice-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 12px 26px;
    margin-bottom: 12px;
    p {
      margin-bottom: 2px;
      font-family: 'Museo Slab';
      font-size: 14px;
    }
  }
}

.meal-plan-payment {
  .payment-input {
    display: flex;
    align-items: center;
    & + .payment-input {
      margin-left: 12px;
    }
  }
  .payment-methods {
    display: flex;
    margin-bottom: 20px;
    > div {
      padding: 8px;
      border: 1px solid rgb(199, 199, 199);
      cursor: pointer;
      margin-right: 8px;
      border-radius: 4px;
      flex-basis: 180px;
      background: #eeeeee;
      min-width: 200px;
      & + div {
        margin-left: 12px;
      }
      p {
        margin-bottom: 0;
        line-height: 12px;
        margin-left: 8px;
        font-size: 16px;
      }
    }
    .active {
      border: 2px solid #000;
      background: white;
      > p {
        color: black;
        font-weight: bold;
      }
    }
  }

  .credit-card-container {
    margin-bottom: 20px;
  }


  .card-element {
    padding: 10px 20px;
    width: 100%;
    padding: 12px;
    box-sizing: border-box;
    font-family: "Museo Sans";
    border: 1px solid #b4b4b4;
    cursor: text;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .saved-cards-select {
    margin-bottom: 20px;
  }

  .checkout-coupon {
    display: grid;
    grid-template-columns: 3fr 1fr;
    font-size: 14px;
  }
}

.form-section {
  .react-datepicker__time-container  {
    width: 160px;
    .react-datepicker__time-box {
      width: 120px !important;
      border-radius: 4px;
    }
  }
}

.meal-plan-back {
  display: flex;
  align-items: center;
  &:hover {
    text-decoration: underline;
  }
  &::before {
    content: '';
    display: inline-block;
    background-image: url("icons/chevron-back-primary.svg");
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
    width: 14px;
    height: 14px;
    margin-right: 6px;
  }
}

.meal-plan-orders-count {
  color: grey;
  font-weight: normal;
}

.meal-orders-list {
  height: 90vh;
  overflow: scroll;
}

.meal-orders-list-buttons {
  display: flex;
  justify-content: space-between;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  margin: 0;
  border-top: 1px solid rgb(192, 192, 192);
  padding: 16px 0;
  > * {
    margin: 0 8px;
  }
}

.notice-card {
  margin-top: 12px;
  color: grey;
}

.form-input.clickable-readonly[readonly] {
  background-color: white !important;
  cursor: pointer !important;
  color: #000 !important;
}

.menu-modal {
  padding: 20px;
  &__heading {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    color: #000;
  }
  .modal-button {
    padding: 6px 10px;
    background: black;
    border-radius: 3px;
    color: white;
    margin-right: 4px;
    cursor: pointer;
    &.primary {
      background: $primary;
    }
  }
}

.remove-meal-button {
  background: $error;
  color: white;
  &:hover {
    background: white;
    color: $error;
    border: 1px solid $error;
  }
}

.recurring-day-meal-plan {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 8px;
  border: 2px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  background-color: #fff;
  color: #333;
  transition: all 0.2s ease;
  font-weight: 500;
  font-size: 14px;
  &::before {
    width: initial !important;
    height: initial !important;
    margin-right: 0 !important;
  }
  &:hover {
    background-color: #f0f8ff;
    border-color: #007cba;
  }
  &.selected {
    background-color: $primary;
    color: #fff;
  }
};
