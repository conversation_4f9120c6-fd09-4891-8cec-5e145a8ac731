class API::MealPlanAttendeesController < ApplicationController

  before_action :fetch_attendee

  def show
    @package_order = @package_order_attendee.present? && @package_order_attendee.order

    if @meal_plan_attendee.present?
      scoped_to = params[:scoped_to] || 'recent_week'
      scoped_time = scoped_to != 'all' && (params[:scoped_time].present? ? Time.zone.parse(params[:scoped_time]) : Time.zone.now)
      lister_options = {
        scoped_to: scoped_to,
        scoped_time: scoped_time,
      }
      @meal_plan = @meal_plan_attendee.meal_plan
      @package_orders = MealPlans::ListPackageOrders.new(meal_plan: @meal_plan, options: lister_options).call
    else
      format.json { render json: { not_found: true }, status: 404 }
    end
  end

private

  def fetch_attendee
    @meal_plan_attendee = MealPlanAttendee.where(uuid: params[:uuid] || params[:id]).first
  end

end