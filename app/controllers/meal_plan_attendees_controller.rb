class MealPlanAttendeesController < ApplicationController

  before_action :fetch_meal_plan

  layout 'team_order'

  def new;end

  def create
    attendee_registration = MealPlans::RegisterAttendee.new(meal_plan: @meal_plan, attendee_params: event_attendee_params).call
    if attendee_registration.success?
      @team_order_attendee = attendee_registration.meal_plan_attendee
      render 'team_order_attendees/success'
    else
      flash[:error] = attendee_registration.errors.compact.join('.') if attendee_registration.errors.present?
      flash[:warning] = attendee_registration.warnings.compact.join('.') if attendee_registration.warnings.present?
      render :new
    end
  end

private

  def fetch_meal_plan
    @meal_plan = MealPlan.where(kind: 'individual', uuid: params[:uuid]).first
  end

  def event_attendee_params
    params.require(:event_attendee).permit(:first_name, :last_name, :email, :level_id)
  end

end
