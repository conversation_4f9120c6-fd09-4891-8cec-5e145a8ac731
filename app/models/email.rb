# == Schema Information
#
# Table name: emails
#
#  id            :integer          not null, primary key
#  recipient     :text
#  options       :text
#  sent_at       :datetime
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  ref           :string(255)
#  fk_id         :integer
#  details       :jsonb
#  template_name :string
#

class Email < ActiveRecord::Base

  FONT_SIZE = '14px'.freeze
  FONT_COLOR = '#241C15'.freeze
  BACKGROUND_COLOR = '#fdfdfd'.freeze
  HR_COLOR = '#ededed'.freeze
  ERROR_COLOR = '#d9534f'.freeze
  LINK_COLOR = '#1f9e86'.freeze
  FONT_FAMILY = 'Mukta, Google Sans, Roboto, Helvetica, sans-serif'.freeze
  HEADER_LOGO = 'https://yordar-production-assets.s3.ap-southeast-2.amazonaws.com/manual/yordar.png'.freeze
  FOOTER_LOGO = 'https://yordar-production-assets.s3.ap-southeast-2.amazonaws.com/manual/yo-tm.png'.freeze
  DEFAULT_AVATAR = 'https://yordar-production-assets.s3.ap-southeast-2.amazonaws.com/manual/avatar.png'.freeze

  HEADER_COLOR_MAP = {
    default: '#000000', # black
    pink: '#f5d7f1',
    cream: '#f4f0eb',
    purple: '#d1c4fb',
    yellow: '#fce13a',
  }.freeze

  DIETARY_COLORS = {
    is_vegetarian: '#d8eacc',
    is_vegan: '#bff457',
    is_gluten_free: '#f8ecb6',
    is_dairy_free: '#f8ecb6',
    is_egg_free: '#F7D5ED',
    is_halal: '#24DCA5',
    is_kosher: '#1F9E86',
    is_nut_free: '#7F53CC',
  }.freeze

  SERIALIZED_FIELDS = [:options].freeze
  SERIALIZED_FIELDS.each do |field|
    define_method "#{field}=" do |value|
      sanitized_value = (value.present? ? value.to_yaml : nil)
      write_attribute(field, sanitized_value)
    end
    define_method field do
      field_data = read_attribute(field)
      field_data.present? ? YAML.safe_load(field_data) : nil
    end
  end

  validates :fk_id, presence: true
  validates :template_name, presence: true, inclusion: { in: EmailTemplate::VALID_TEMPLATE_NAMES }

  def self.view_template(template_name)
    return nil if EmailTemplate::VALID_TEMPLATE_NAMES.exclude?(template_name)

    template_name = template_name.gsub('team-order-', 'team_order-').gsub('meal-plan-', 'meal_plan-')
    name_split = template_name.split('-')
    "emails/#{name_split[0]}/#{name_split[1..].join('_')}"
  end

  def subject
    return nil if details.blank?

    details['subject']
  end

end
