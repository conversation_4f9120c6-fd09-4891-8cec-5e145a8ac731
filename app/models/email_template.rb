# == Schema Information
#
# Table name: email_templates
#
#  id           :bigint           not null, primary key
#  name         :string           not null
#  account_type :string
#  kind         :string
#  variations   :string           default([]), is an Array
#  can_override :boolean          default(FALSE)
#  position     :integer
#
class EmailTemplate < ApplicationRecord

  CUSTOMER_EMAIL_TEMPLATES = %w[
    customer-welcome
    customer-customer_quote
    customer-new_order
    customer-order_confirmed
    customer-order_quote
    customer-initial_closure
    customer-final_closure
    customer-invoice_tax_receipt
    customer-order_invoice
    customer-overdue_invoice
    customer-order_review_invitation
    customer-order_summary
    customer-pending_orders
    customer-public_holiday_orders
    customer-company_customer_invitation
    customer-company_customer_invitation_link
    customer-admin_invitation
    customer-company_team_admin_request
    customer-company_team_admin_welcome
    customer-loading_dock_request
    customer-meal_plan_reminder
    customer-order_charge_failed
  ].freeze

  SUPPLIER_EMAIL_TEMPLATES = %w[
    supplier-welcome
    supplier-contactless_delivery_info
    supplier-final_closure
    supplier-initial_closure
    supplier-new_order
    supplier-new_team_order
    supplier-order_changed
    supplier-order_adjusted
    supplier-order_cancelled
    supplier-order_reactivated
    supplier-order_skipped
    supplier-order_summary
    supplier-orders_reminder
    supplier-recurring_order_reminder
    supplier-purchase_order_summary
    supplier-review_notification
    supplier-loading_dock
    supplier-menu_reminder
  ].freeze

  INVOICE_EMAIL_TEMPLATES = %w[
    invoice-critical_overdue_invoices
  ].freeze

  TEAM_ORDER_EMAIL_TEMPLATES = %w[
    customer-team_admin_request
    customer-team_admin_welcome
    customer-new_team_order
    customer-new_team_order_package
    customer-team_order_submission
    team-order-admin-registration
    team-order-admin-anonymous_attendees_notification
    team-order-admin-cutoff_notification
    team-order-admin-team_order_extension
    team-order-attendee-cutoff_notification
    team-order-attendee-delivery_notification
    team-order-attendee-invite
    team-order-attendee-package_invite
    meal-plan-attendee_registration
    team-order-attendee-order_cancelled
    team-order-attendee-order_checkout
    team-order-attendee-removed_notification
    supplier-team_order_created
    supplier-team_order_cutoff
  ].freeze

  USER_EMAIL_TEMPLATES = %w[
    user-confirm_account
    user-lost_password
  ].freeze

  ADMIN_EMAIL_TEMPLATES = %w[
    admin-customer_quote
    admin-rejected_order
    admin-rejected_order_quote
    admin-order_charge_failed
    admin-woolworths_checkout_failed
    yordar-failed_stripe_payment
    yordar-failed_xero_invoices
    yordar-new_supplier_registration
    yordar-order_review_summary
    yordar-pending_orders
    yordar-rake_task_errors
    yordar-rate_card_amended
    yordar-menu_item_amended
    yordar-supplier_menu_export
    yordar-order_confirmation_check
    yordar-managed_order
    yordar-bank_details_changed
    yordar-staffing_schedule
    yordar-staffing_log
    yordar-staffing_log_accounts
    yordar-reminder
    yordar-staff_details
    yordar-deprecated_email_template
  ].freeze
  # yordar-deprecated_email_template used for setting value of old deprecated email records

  VALID_TEMPLATE_NAMES = (CUSTOMER_EMAIL_TEMPLATES + SUPPLIER_EMAIL_TEMPLATES + INVOICE_EMAIL_TEMPLATES + TEAM_ORDER_EMAIL_TEMPLATES + USER_EMAIL_TEMPLATES + ADMIN_EMAIL_TEMPLATES).freeze
  VALID_ACCOUNT_TYPES = %w[CustomerProfile SupplierProfile].freeze
  VALID_KINDS = %w[order billing team_admin team_order_attendee].freeze

  validates :name, presence: true, inclusion: { in: VALID_TEMPLATE_NAMES }, uniqueness: { scope: :account_type }
  validates :account_type, presence: true, inclusion: { in: VALID_ACCOUNT_TYPES }
  validates :kind, inclusion: { in: VALID_KINDS }, allow_nil: true

end
