# required by report upload
require "#{Rails.root}/app/helpers/cloudinary_helper"
# == Schema Information
#
# Table name: customer_profiles
#
#  id                            :integer          not null, primary key
#  contact_phone                 :string(255)
#  mobile                        :string(255)
#  created_at                    :datetime         not null
#  updated_at                    :datetime         not null
#  company_id                    :integer
#  company_name                  :string(255)
#  fax                           :string(255)
#  notes                         :string(255)
#  customer_name                 :string(255)
#  team_admin                    :boolean          default(FALSE)
#  company_team_admin            :boolean          default(FALSE)
#  admin_notes                   :text
#  role                          :string
#  uuid                          :string
#  stripe_token                  :string
#

class CustomerProfile < ActiveRecord::Base

	include CloudinaryHelper

	VALID_STAFF_ROLES = ['Pantry Manager', 'Account Manager'].freeze
	VALID_CUSTOMER_ROLES = ['Administrative Assistant', 'HR', 'Office Manager', 'Workplace Experience', 'Facilities & Workplace Services', 'Sales & Marketing', 'Other'].freeze
	VALID_ROLES = VALID_STAFF_ROLES + VALID_CUSTOMER_ROLES
	BILLING_NEEDS_FIELDS = %i[order_summaries summary_report invoice_spreadsheet].freeze
	BILLING_FLAG_FIELDS = %i[billing_day invoice_order_grouping hide_delivery_pricing].freeze

	CUSTOMER_REVIEW_FLAGS = %i[cancel_review_requests].freeze
	CUSTOMER_HAS_FLAGS = %i[has_gst_split_invoicing].freeze
	CUSTOMER_REQUIRE_FLAGS = %i[requires_department_identity requires_supplier_markup requires_loading_dock_code].freeze
	CUSTOMER_DEFAULT_FLAGS = %i[default_orders_view accounting_software].freeze
	CUSTOMER_FLAG_FIELDS = CUSTOMER_REVIEW_FLAGS + CUSTOMER_HAS_FLAGS + CUSTOMER_REQUIRE_FLAGS + CUSTOMER_DEFAULT_FLAGS

	# validates_associated :profile
	validates :role, inclusion: { in: VALID_ROLES }, allow_blank: true

	has_and_belongs_to_many :credit_cards
	has_and_belongs_to_many :supplier_profiles

	has_many :customer_profile_menu_items
	has_many :favourite_menu_items, class_name: 'CustomerProfileMenuItem', foreign_key: :customer_profile_id

	belongs_to :company

	has_many :admin_access_permissions, class_name: 'AccessPermission', foreign_key: :admin_id
	has_many :active_admin_access_permissions, -> { where(access_permissions: { active: true }) }, class_name: 'AccessPermission', foreign_key: :admin_id
	has_many :active_adminable_customer_profiles, through: :active_admin_access_permissions, source: :customer_profile
	has_many :access_permissions_as_customer, class_name: 'AccessPermission', foreign_key: :customer_profile_id

	# acts as profile
	has_one :profile, as: :profileable, dependent: :destroy
	accepts_nested_attributes_for :profile

	# because we want to use the user
	has_one :user, through: :profile
	accepts_nested_attributes_for :user

	has_one :billing_details
	has_one :customer_flags, class_name: 'CustomerFlags'
	has_one :staff_details, class_name: 'StaffDetails'

	has_many :meal_plans
	has_many :meal_plan_attendees, through: :meal_plans, source: :attendees

	has_many :orders, inverse_of: :customer_profile
	has_many :team_orders, -> { where(order_variant: %w[team_order recurring_team_order]) }, class_name: 'Order', foreign_key: :customer_profile_id
	has_many :team_order_attendees, through: :team_orders
	has_many :pantry_manager_orders, class_name: 'Order', inverse_of: :pantry_manager
	has_many :invoices

	has_many :event_attendees, class_name: 'EventAttendee', dependent: :destroy, foreign_key: :team_admin_id
	has_many :event_teams

	has_many :customer_purchase_orders
	has_many :saved_addresses
	has_many :employee_surveys
	has_many :loading_docks

	has_many :notification_preferences, class_name: 'Notification::Preference', as: :account
	has_many :report_sources, class_name: 'Report::Source', as: :source
	has_many :budgets, class_name: 'CustomerBudget'

	has_many :documents, as: :documentable
	has_many :quotes, class_name: 'CustomerQuote'

	has_many :delivery_overrides
	has_many :supplier_markup_overrides, class_name: 'Supplier::MarkupOverride', as: :overridable
	has_many :promotion_subscriptions, as: :subscriber

	# favourite suppliers
	has_many :favourite_supplier_records, -> { where(kind: 'normal') }, class_name: 'FavouriteSupplier', as: :favouriter
	has_many :favourite_suppliers, through: :favourite_supplier_records, source: :supplier_profile

	# favourite team suppliers
	has_many :favourite_team_supplier_records, -> { where(kind: 'team_order') }, class_name: 'FavouriteSupplier', as: :favouriter
	has_many :favourite_team_suppliers, through: :favourite_team_supplier_records, source: :supplier_profile

	# Scopes
	scope :with_a_company, -> { where.not(company_id: nil) }

	# callbacks
	after_save :welcome_team_admin, if: -> { saved_change_to_team_admin? }
	after_save :welcome_company_team_admin, if: -> { saved_change_to_company_team_admin? }
	after_save :sync_contact_to_hubspot, if: -> { saved_change_to_company_team_admin? }
	before_destroy :cancel_orders

	delegate :frequency, to: :billing_details, prefix: :billing, allow_nil: true
	delegate(*BILLING_NEEDS_FIELDS, to: :billing_details, prefix: :needs, allow_nil: true)
	delegate(*BILLING_FLAG_FIELDS, to: :billing_details, allow_nil: true)
	delegate(*CUSTOMER_FLAG_FIELDS, to: :customer_flags)

	delegate :requires_po, to: :company, allow_nil: true

	delegate :email, to: :user, allow_nil: true
	delegate :suburb, to: :user, allow_nil: true

	def name
		user&.name || 'A customer'
	end

	def country_of_origin
		user&.country_of_origin || 'AU'
	end

	# Returns this order's customer's company name or registered company name or app user's name
	def customer_or_company_name
		company&.name || company_name || name
	end

	def email_recipient
		recipient = user.present? ? user.email_recipient : yordar_credentials(:yordar, :admin_email)
		recipient.to_s.gsub(/\r|\n/, '')
	end

	# returns first name of the customer
	def email_salutation
		user&.firstname || 'there'
	end

	def can_pay_on_account?
		(company.present? && company.can_pay_on_account?) ||
			 credit_cards.where(enabled: true, auto_pay_invoice: true).reject(&:expired?).present?
	end

	def can_pay_by_credit_card?
		company.blank? || company.can_pay_by_credit_card?
	end

	def invoice_accounting_software
		@_accouting_software ||= begin
			software = customer_flags&.accounting_software.presence || company&.accounting_software
			software == 'none' ? nil : software
		end
	end

private

	# callback methods
	def welcome_team_admin
		if team_admin? && Email.where(fk_id: id, ref: "customer-team_admin_welcome-#{id}").blank?
			Customers::Emails::SendTeamAdminWelcomeEmail.new(customer: self).delay(queue: :notifications).call
		end
	end

	def welcome_company_team_admin
		if company_team_admin? && Email.where(fk_id: id, ref: "customer-company_team_admin_welcome-#{id}").blank? && active_admin_access_permissions.where(scope: %w[pantry_manager account_manager]).blank?
			Customers::Emails::SendCompanyTeamAdminWelcomeEmail.new(customer: self).delay(queue: :notifications).call
		end
	end

	def sync_contact_to_hubspot
		return if user.blank?

		# only call syncer once
		hubspot_handler = "%object:Hubspot::SyncContact%value_before_type_cast: #{user.id}%method_name: :call%"
		if (Rails.env.production? || Rails.env.test?) && Delayed::Job.where('handler ilike ?', hubspot_handler).where(locked_at: nil, failed_at: nil).blank?
			Hubspot::SyncContact.new(contact: user).delay.call
		end
	end

	# before deletion of a customer profile, lets set its orders to cancelled
	def cancel_orders
		Order.where(customer_profile_id: id).each do |order|
			# cancel the orders and set the customer_profile_id
			order.update(status: 'cancelled', customer_profile_id: 1) # TODO: dummy customer profile set for now
		end
	end

end
