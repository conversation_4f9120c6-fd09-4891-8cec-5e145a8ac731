# == Schema Information
#
# Table name: event_attendees
#
#  id            :integer          not null, primary key
#  first_name    :string(255)
#  last_name     :string(255)
#  email         :string(255)
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  team_admin_id :integer
#  active        :boolean          default(TRUE)
#

class EventAttendee < ActiveRecord::Base

	validates :first_name, presence: true
	validates :email, uniqueness: { scope: :team_admin_id, case_sensitive: false }, presence: true
	validates :email, format: { with: /\A([^@\s]+)@((?:[-a-z0-9]+\.)+[a-z]{2,})\z/i, on: :create }

	has_many :team_order_attendees, dependent: :destroy
	has_many :orders, through: :team_order_attendees
	has_many :meal_plan_attendees, dependent: :destroy
	has_many :meal_plans, through: :meal_plan_attendees
	has_many :credit_cards
	belongs_to :team_admin, class_name: 'CustomerProfile', foreign_key: :team_admin_id

	has_many :event_attendee_teams
	has_and_belongs_to_many :event_teams

	def name
		name_str = first_name
		name_str += " #{last_name}" if last_name.present?
		name_str
	end

	def self.team_admin_as_attendee(team_admin:)
		team_admin.event_attendees.where(first_name: team_admin.name, last_name: '(team admin)', email: team_admin.user.email).first_or_initialize
	end

	def is_team_admin?
		email == team_admin.user&.email
	end
end
