class MealPlanAttendee < ApplicationRecord

  VALID_ATTENDEE_STATUSES = %w[registered declined cancelled].freeze

  validates :meal_plan, presence: true
  validates :event_attendee, presence: true
  validates :uuid, presence: true
  validates :status, presence: true, inclusion: { in: VALID_ATTENDEE_STATUSES }

  belongs_to :meal_plan
  belongs_to :event_attendee

  delegate :first_name, to: :event_attendee
  delegate :last_name, to: :event_attendee
  delegate :name, to: :event_attendee
  delegate :email, to: :event_attendee

end
