class MealPlans::Emails::SendAttendeeRegistrationEmail < Notifications::Base
  include ActionView::Helpers::NumberHelper

  EMAIL_TEMPLATE = 'meal-plan-attendee_registration'.freeze

  def initialize(meal_plan_attendee:, meal_plan: nil)
    @meal_plan_attendee = meal_plan_attendee
    @meal_plan = meal_plan || meal_plan_attendee.meal_plan
    @result = Result.new    
  end

  def call
    return result if !can_send?

    begin
      send_email
    rescue => exception
      error_message = "Failed to send meal plan ##{meal_plan.id} registration email to attendee - #{meal_plan_attendee.uuid}, #{meal_plan_attendee.name}"
      log_errors(exception: exception, message: error_message, sentry: false)
      result.errors << error_message
    end
  end

private

  attr_reader :meal_plan_attendee, :meal_plan, :result

  def can_send?
    case
    when meal_plan.blank?
      result.errors << 'Cannot notify without a meal plan'
    when meal_plan_attendee.blank?
      result.errors << 'Cannot notify without an attendee'
    end
    result.errors.blank?
  end

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: meal_plan_attendee.email,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Sent meal plan ##{meal_plan.id} registration email to attendee #{meal_plan_attendee.uuid}, #{meal_plan_attendee.name}"
      result.sent_notification = email_sender.email
    else
      result.errors += email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    "Yordar: Your registration to the meal plan orders - #{meal_plan.name}"
  end

  def email_options
    {
      fk_id: meal_plan_attendee.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      meal_plan: deep_struct(meal_plan_data),
      attendee: deep_struct(attendee_data),
      meal_plan_orders: [], # fetch meal plan orders
      
      header_color: :purple,
    }
  end

  def meal_plan_data
    {
      id: meal_plan.id,
      name: meal_plan.name,
      budget: number_to_currency(meal_plan.budget, precision: 2),
      hide_budget: meal_plan.hide_budget,
      delivery_address: meal_plan.delivery_address_arr.join(',<br>'),
    }
  end

  def attendee_data
    {
      name: meal_plan_attendee.first_name,
      package_url: url_helper.team_order_attendee_package_url(code: meal_plan_attendee.uuid, host: next_app_host, tld_length: 2), # need a new route
    }
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{meal_plan.id}-#{meal_plan_attendee.id}"
  end

end
