class MealPlans::ListPackageOrders

  def initialize(meal_plan:, options: {})
    @meal_plan = meal_plan
    @options = [default_options, options].inject(&:merge)
  end

  def call
    if meal_plan.blank? || meal_plan.kind != 'individual'
      return Order.none
    end

    @package_orders = meal_plan.orders.joins(:team_order_detail)
    filter_linked_orders
    filter_active_orders if options[:active_only].present?
    scope_orders if options[:scoped_to].present? && options[:scoped_time].present?
    sort_orders
    package_orders.distinct
  end

private

  attr_reader :meal_plan, :options, :package_orders

  def filter_linked_orders
    @package_orders = package_orders.where(team_order_details: { package_id: meal_plan.uuid })
  end

  def filter_active_orders
    @package_orders = package_orders.where(status: %w[pending new amended])
  end

  def scope_orders
    scoped_time = options[:scoped_time].present? && options[:scoped_time].is_a?(String) ? Time.parse(options[:scoped_time]) : options[:scoped_time]
    case options[:scoped_to]
    when 'future_only'
      @package_orders = package_orders.where('delivery_at >= ?', scoped_time)
    when 'recent_month'
       @package_orders = package_orders.where(delivery_at: [scoped_time.beginning_of_month..scoped_time.end_of_month])
    when 'recent_fortnight'
       @package_orders = package_orders.where(delivery_at: [scoped_time.beginning_of_week..(scoped_time + 1.week).end_of_week])
    when 'recent_week'
      @package_orders = package_orders.where(delivery_at: [scoped_time.beginning_of_week..scoped_time.end_of_week])
    # else # all # do nothing
    end
  end

  def sort_orders
    @package_orders = case
    when options[:order_by].present? && options[:order_by].is_a?(Hash)
      package_orders.order(**options[:order_by])
    when options[:order_by].present?
      package_orders.order(options[:order_by])
    else
      package_orders.order(delivery_at: :asc, id: :asc)
    end
  end

  def default_options
    {
      active_only: false,
      order_by: nil,
      scoped_to: nil,
      scoped_time: Time.zone.now,
    }
  end

end
