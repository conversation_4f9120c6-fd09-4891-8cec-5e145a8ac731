class MealPlans::<PERSON><PERSON><PERSON><PERSON><PERSON>

  def initialize(meal_plan: ,attendee_params:, notify_attendee: true)
    @meal_plan = meal_plan
    @attendee_params = attendee_params
    @notify_attendee = notify_attendee
    @result = Result.new
  end

  def call
    if fetch_event_attendee && can_attach_attendee?
      if meal_plan_attendee.update(sanitized_attributes)
        result.meal_plan_attendee = meal_plan_attendee
        notify_meal_plan_attendee
      else
        result.errors += meal_plan_attendee.errors.full_messages
      end
    end
    result
  end

private

  attr_reader :meal_plan, :attendee_params, :event_attendee, :meal_plan_attendee, :notify_attendee, :result

  def existing_event_attendee
    return nil if attendee_params[:email].blank?

    @_existing_event_attendee ||= attendee_params[:email].present? && team_admin.event_attendees.where('lower(email) = ?', attendee_params[:email].downcase).first
  end

  def fetch_event_attendee
    case
    when meal_plan.blank?
      result.errors << 'This event invite link has expired'
    when attendee_params[:email].present? && attendee_params[:email] == team_admin.email
      result.warnings << 'You are already registered as an attendee for this event, please check your email for an order invite link'
    when existing_event_attendee.present?
      result.event_attendee = @event_attendee = existing_event_attendee
    else
      attendee_creator = EventAttendees::Upsert.new(event_attendee_params: attendee_params.except(:level_id), team_admin: team_admin).call
      if attendee_creator.success?
        result.event_attendee = @event_attendee = attendee_creator.event_attendee
      else
        result.errors += attendee_creator.errors
      end
    end
    result.errors.blank? && result.warnings.blank?
  end

  def can_attach_attendee?
    fetch_meal_plan_attendee
    case
    when meal_plan_attendee.present? && meal_plan_attendee.persisted?
      result.warnings << 'You are already registered as an attendee for this event, please check your email for an order invite link or contact your team admin'
      result.meal_plan_attendee = meal_plan_attendee
      notify_meal_plan_attendee
    end
    result.errors.blank? && result.warnings.blank?
  end


  def sanitized_attributes
    [default_attributes, level_attributes].inject(&:merge)
  end

  def default_attributes
    {
      status: 'registered',
      uuid: SecureRandom.uuid,
    }
  end

  def level_attributes
    return {} # still working on attendee levels

    level_id = attendee_params[:level_id]
    return {} if level_id.blank? || meal_plan.attendee_levels.blank?

    return {} if meal_plan.attendee_levels.map(&:id).exclude?(level_id.to_i)

    {
      team_order_level_id: level_id
    }
  end

  def fetch_meal_plan_attendee
    @meal_plan_attendee = meal_plan.attendees.where(event_attendee: event_attendee).first_or_initialize
  end

  def notify_meal_plan_attendee
    return if !notify_attendee

    MealPlans::Emails::SendAttendeeRegistrationEmail.new(meal_plan: meal_plan, meal_plan_attendee: meal_plan_attendee).delay(queue: :notifications).call
  end

  def team_admin
    @_team_admin ||= meal_plan.customer_profile
  end

  class Result
    attr_accessor :event_attendee, :meal_plan_attendee, :errors, :warnings

    def initialize
      @event_attendee = nil
      @meal_plan_attendee = nil
      @errors = []
      @warnings = []
    end

    def success?
      errors.blank? && warnings.blank? && event_attendee.present? && meal_plan_attendee.present?
    end
  end

end
