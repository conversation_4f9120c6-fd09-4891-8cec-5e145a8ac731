class TeamOrderAttendees::Notifications::CutoffReminder < TeamOrders::Notifications::Cutoff

  RECURRING_NOTIFICATIONS = %w[24hr 4hr 30m].freeze
  NORMAL_NOTIFICATIONS = %w[4hr 2hr 30m].freeze

  def initialize(cutoff_time:, time: Time.zone.now)
    super
    @sent_notifications = []
  end

  def call
    pending_team_orders.each do |team_order|
      next if !can_notify_attendees_for(team_order)

      notifiable_attendees_for(team_order).each do |team_order_attendee|
        if team_order.is_recurring_team_order? && team_order_attendee.order != team_order
          team_order_attendee = create_recurring_attendee(team_order_attendee, team_order)
        end
        sent_notifications << TeamOrderAttendees::Emails::SendCutOffEmail.new(team_order: team_order, team_order_attendee: team_order_attendee, cutoff_time: cutoff_time).call
      end
    end
    sent_notifications
  end

private

  attr_reader :sent_notifications

  def pending_team_orders
    pending_orders = Order.where(order_variant: %w[team_order recurring_team_order], status: 'pending')
    pending_orders = pending_orders.where('delivery_at between ? and ?', time, (time + DELIVERY_THRESHOLD))
    pending_orders
  end

  def can_notify_attendees_for(team_order)
    is_notifiable(team_order) && is_within_cutoff_notification_range(team_order)
  end

  def is_notifiable(team_order)
    case
    when team_order.is_recurring_team_order?
      RECURRING_NOTIFICATIONS.include?(cutoff_time)
    else
      NORMAL_NOTIFICATIONS.include?(cutoff_time)
    end
  end

  def is_within_cutoff_notification_range(team_order)
    lead_time_fetcher = Orders::FetchLeadTime.new(order: team_order).call
    (lead_time_fetcher.lead_time - TeamOrderAttendee::SOFT_CUTOFF_THRESHOLD).between?(start_hours, end_hours)
  end

  def notifiable_attendees_for(team_order)
    if team_order.is_recurring_team_order?
      grouped_attendees = package_attendees_for(team_order).group_by(&:event_attendee)
      notifiable_attendees = grouped_attendees.map do |event_attendee, team_order_attendees|
        next if !event_attendee.active?

        notifiable_attendee = team_order_attendees.detect do |attendee|
          attendee.order == team_order
        end
        notifiable_attendee ||= team_order_attendees.detect do |attendee|
          attendee.status == 'ordered'
        end
        notifiable_attendee
      end.compact
    else
      notifiable_attendees = team_order.team_order_attendees
      notifiable_attendees = notifiable_attendees.where(status: %w[invited pending])
      notifiable_attendees = notifiable_attendees.where("team_order_attendees.#{attendee_reminder_attribute} is NULL")
    end
    notifiable_attendees
  end

  def package_attendees_for(team_order)
    current_non_pending_attendees = team_order.team_order_attendees.where(status: %w[ordered declined cancelled])
    current_notified_attendees = team_order.team_order_attendees.where("team_order_attendees.#{attendee_reminder_attribute} is NOT NULL")

    package_attendees = TeamOrderAttendee.joins(order: :team_order_detail)
    package_attendees = package_attendees.where(team_order_details: { package_id: team_order.package_id })
    package_attendees = package_attendees.where.not(event_attendee: (current_non_pending_attendees + current_notified_attendees).map(&:event_attendee))
    package_attendees.distinct
  end

  def create_recurring_attendee(team_order_attendee, team_order)
    return team_order_attendee if yordar_credentials(:skip_attendee_creation_for_test).present? # only generate the recurring team order unless SKIP ATTENDEE CREATION IS SET TO TRUE
    attendee)

    attendee_pacakage_code = team_order_attendee.meal_plan_attendee&.uuid || team_order_attendee.uniq_code
    team_order_params = { code: attendee_pacakage_code, event_id: team_order.unique_event_id }
    TeamOrderAttendees::FetchWithinPackage.new(team_order_params: team_order_params).call
  end

  def attendee_reminder_attribute
    "cutoff_#{cutoff_time}_reminder"
  end

end
