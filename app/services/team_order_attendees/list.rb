class TeamOrderAttendees::List

  def initialize(options: {}, includes: [])
    @filter_options = [default_options, options.symbolize_keys].inject(&:merge)
    @includes = includes
  end

  def call
    @attendees = base_attendees
    filter_by_order if filter_options[:order].present? || filter_options[:orders].present?
    filter_active_attendees if filter_options[:active_only].present?

    add_package_attendees if filter_options[:include_package_attendees].present?
    sort_attendees if filter_options[:sort_by].present?

    # admin attendee
    add_admin_attendee if filter_options[:order].present? && filter_options[:include_admin].present?

    attendees
  end

private

  attr_reader :filter_options, :includes
  attr_accessor :attendees

  def base_attendees
    _base_attendees = TeamOrderAttendee.all
    _base_attendees = _base_attendees.includes(:event_attendee) if filter_options[:sort_by].present? && filter_options[:sort_by] == 'contact_name'
    _base_attendees
  end

  def filter_by_order
    orders = filter_options[:order].presence || filter_options[:orders]
    return if orders.blank?

    @attendees = attendees.where(order: orders)
  end

  def filter_active_attendees
    @attendees = attendees.where.not(status: %w[declined cancelled])
  end

  def sort_attendees
    @attendees = begin
      if filter_options[:sort_by] == 'contact_name'
        attendees.sort_by{|attendee| [attendee.first_name, attendee.last_name] }
      else
        attendees.sort_by{|attendee| attendee.send(filter_options[:sort_by]) }
      end
    end
  end

  def add_package_attendees
    team_order = filter_options[:order]
    return if team_order.blank? || !team_order.is_package_order?

    meal_plan = team_order.meal_plan
    package_attendees = team_order.meal_plan.attendees
    package_attendees = package_attendees.where.not(event_attendees: @attendees.map(&:event_attendee))

    @attendees += package_attendees
  end

  def add_admin_attendee
    admin_as_attendee = TeamOrderAttendees::Fetch.new(attendee_code: filter_options[:order].unique_event_id, profile: filter_options[:include_admin]).call
    @attendees = attendees.to_a.unshift(admin_as_attendee)
  end

  def default_options
    {
      order: nil,
      orders: [],
      active_only: false,
      sort_by: nil,
      include_admin: nil,
      include_package_attendees: false,
    }
  end

end